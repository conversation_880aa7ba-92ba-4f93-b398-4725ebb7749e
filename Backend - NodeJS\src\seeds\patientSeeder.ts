import Patient, { IPatient } from '../models/Patient';

const patientData: Partial<IPatient>[] = [
  {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    date_of_birth: new Date('1985-06-15'),
    gender: 'male',
    phone: '******-2001',
    email: '<EMAIL>',
    address: '123 Main Street, Anytown, State 12345',
    emergency_contact: {
      name: '<PERSON>',
      relationship: 'Spouse',
      phone: '******-2002',
      email: '<EMAIL>'
    },
    insurance_info: {
      provider: 'Blue Cross Blue Shield',
      policy_number: 'BC123456789',
      group_number: 'GRP001',
      expiry_date: new Date('2024-12-31')
    }
  },
  {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    date_of_birth: new Date('1992-03-22'),
    gender: 'female',
    phone: '******-2003',
    email: '<EMAIL>',
    address: '456 Oak Avenue, Springfield, State 12346',
    emergency_contact: {
      name: '<PERSON>',
      relationship: 'Father',
      phone: '******-2004',
      email: '<EMAIL>'
    },
    insurance_info: {
      provider: '<PERSON><PERSON><PERSON>',
      policy_number: 'AE987654321',
      group_number: 'GRP002',
      expiry_date: new Date('2024-11-30')
    }
  },
  {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    date_of_birth: new Date('1975-11-08'),
    gender: 'male',
    phone: '******-2005',
    email: '<EMAIL>',
    address: '789 <PERSON> Street, Riverside, State 12347',
    emergency_contact: {
      name: 'Susan <PERSON>',
      relationship: 'Wife',
      phone: '******-2006',
      email: '<EMAIL>'
    },
    insurance_info: {
      provider: 'Cigna',
      policy_number: 'CG456789123',
      group_number: 'GRP003',
      expiry_date: new Date('2024-10-31')
    }
  },
  {
    first_name: 'Emily',
    last_name: 'Davis',
    date_of_birth: new Date('2010-08-14'),
    gender: 'female',
    phone: '******-2007',
    email: '<EMAIL>',
    address: '321 Elm Street, Hillside, State 12348',
    emergency_contact: {
      name: 'Michael Davis',
      relationship: 'Father',
      phone: '******-2008',
      email: '<EMAIL>'
    },
    insurance_info: {
      provider: 'United Healthcare',
      policy_number: 'UH789123456',
      group_number: 'GRP004',
      expiry_date: new Date('2024-09-30')
    }
  },
  {
    first_name: 'William',
    last_name: 'Brown',
    date_of_birth: new Date('1955-12-03'),
    gender: 'male',
    phone: '******-2009',
    email: '<EMAIL>',
    address: '654 Maple Drive, Lakewood, State 12349',
    emergency_contact: {
      name: 'Margaret Brown',
      relationship: 'Wife',
      phone: '******-2010',
      email: '<EMAIL>'
    },
    insurance_info: {
      provider: 'Medicare',
      policy_number: 'MC321654987',
      group_number: 'GRP005',
      expiry_date: new Date('2024-12-31')
    }
  },
  {
    first_name: 'Sarah',
    last_name: 'Wilson',
    date_of_birth: new Date('1988-04-17'),
    gender: 'female',
    phone: '******-2011',
    email: '<EMAIL>',
    address: '987 Cedar Lane, Fairview, State 12350',
    emergency_contact: {
      name: 'David Wilson',
      relationship: 'Husband',
      phone: '******-2012',
      email: '<EMAIL>'
    },
    insurance_info: {
      provider: 'Humana',
      policy_number: 'HU654987321',
      group_number: 'GRP006',
      expiry_date: new Date('2024-08-31')
    }
  },
  {
    first_name: 'Christopher',
    last_name: 'Lee',
    date_of_birth: new Date('1970-09-25'),
    gender: 'male',
    phone: '******-2013',
    email: '<EMAIL>',
    address: '147 Birch Street, Greenfield, State 12351',
    emergency_contact: {
      name: 'Lisa Lee',
      relationship: 'Wife',
      phone: '******-2014',
      email: '<EMAIL>'
    },
    insurance_info: {
      provider: 'Kaiser Permanente',
      policy_number: 'KP987321654',
      group_number: 'GRP007',
      expiry_date: new Date('2024-07-31')
    }
  },
  {
    first_name: 'Jessica',
    last_name: 'Taylor',
    date_of_birth: new Date('1995-01-12'),
    gender: 'female',
    phone: '******-2015',
    email: '<EMAIL>',
    address: '258 Walnut Avenue, Westside, State 12352',
    emergency_contact: {
      name: 'Jennifer Taylor',
      relationship: 'Mother',
      phone: '******-2016',
      email: '<EMAIL>'
    },
    insurance_info: {
      provider: 'Anthem',
      policy_number: 'AN147258369',
      group_number: 'GRP008',
      expiry_date: new Date('2024-06-30')
    }
  },
  {
    first_name: 'Daniel',
    last_name: 'Anderson',
    date_of_birth: new Date('1982-07-30'),
    gender: 'male',
    phone: '******-2017',
    email: '<EMAIL>',
    address: '369 Spruce Road, Eastpark, State 12353',
    emergency_contact: {
      name: 'Amanda Anderson',
      relationship: 'Wife',
      phone: '******-2018',
      email: '<EMAIL>'
    },
    insurance_info: {
      provider: 'Molina Healthcare',
      policy_number: 'MH258369147',
      group_number: 'GRP009',
      expiry_date: new Date('2024-05-31')
    }
  },
  {
    first_name: 'Ashley',
    last_name: 'Martinez',
    date_of_birth: new Date('1993-10-05'),
    gender: 'female',
    phone: '******-2019',
    email: '<EMAIL>',
    address: '741 Poplar Street, Northside, State 12354',
    emergency_contact: {
      name: 'Roberto Martinez',
      relationship: 'Brother',
      phone: '******-2020',
      email: '<EMAIL>'
    },
    insurance_info: {
      provider: 'Medicaid',
      policy_number: 'MD369147258',
      group_number: 'GRP010',
      expiry_date: new Date('2024-04-30')
    }
  }
];

export async function seedPatients(): Promise<void> {
  try {
    await Patient.deleteMany({});
    await Patient.insertMany(patientData);
  } catch (error) {
    throw error;
  }
} 