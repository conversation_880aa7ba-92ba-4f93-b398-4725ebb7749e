# clinicpro-frontend-v2

A modern clinic management system frontend built with React, TypeScript, and Vite.

## Prerequisites

- Node.js (version 18 or higher)
- npm or yarn package manager

## Installation

1. Clone the repository
```bash
git clone <repository-url>
cd clinicpro-frontend-v2
```

2. Install dependencies
```bash
npm install
```

## Development

Start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:5173`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run test` - Run tests
- `npm run typecheck` - Run TypeScript type checking
- `npm run format.fix` - Format code with Prettier

## Tech Stack

- React 18
- TypeScript
- Vite
- Tailwind CSS
- Radix UI
- React Router
- React Hook Form
- Axios
- React Query
