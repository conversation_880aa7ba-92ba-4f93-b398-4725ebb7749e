# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
out/

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
.env.staging

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# IDE/Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Package manager lock files (keep one, ignore others)
# yarn.lock
# pnpm-lock.yaml

# Upload directories
uploads/
public/uploads/
static/uploads/

# Database files
*.sqlite
*.sqlite3
*.db

# Docker files (since Docker was removed)
docker-compose*.yml
Dockerfile*
.dockerignore*

# PM2 files
ecosystem.config.js
.pm2

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Backup files
*.bak
*.backup
*.old

# Test coverage
coverage/
.nyc_output/

# Jest
jest_html_reporters.html

# ESLint
.eslintcache

# Optional stylelint cache
.stylelintcache

# SvelteKit build / generate output
.svelte-kit

# Local development
.local

# Webpack
.webpack/

# Vite
.vite/

# Rollup
.rollup.cache/

# Certificates
*.pem
*.key
*.crt
*.cert

# Local configuration
config/local.json
config/local.js
config/local.ts 