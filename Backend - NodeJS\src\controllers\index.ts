export { UserController } from './userController';
export { PatientController } from './patientController';
export { AppointmentController } from './appointmentController';
export { MedicalRecordController } from './medicalRecordController';
export { InvoiceController } from './invoiceController';
export { PaymentController } from './paymentController';
export { PayrollController } from './payrollController';
export { InventoryController } from './inventoryController';
export { LeadController } from './leadController';
export { PrescriptionController } from './prescriptionController';
export { TestCategoryController } from './testCategoryController';
export { SampleTypeController } from './sampleTypeController';
export { TestMethodologyController } from './testMethodologyController';
export { TurnaroundTimeController } from './turnaroundTimeController';
export { TestController } from './testController';
export { TestReportController } from './testReportController';
export { DepartmentController } from './departmentController';
export { LabVendorController } from './labVendorController';
export { DashboardController } from './dashboardController';
export { ReceptionistController } from './receptionistController';
export { default as SettingsController } from './settingsController'; 