version: '3.8'

services:
  # MongoDB Database (same as production)
  mongodb:
    image: mongo:7.0
    container_name: clinicpro-mongodb-dev
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-clinicpro123}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE:-clinic-pro}
    ports:
      - "${MONGO_PORT:-27017}:27017"
    volumes:
      - mongodb_data_dev:/data/db
      - mongodb_config_dev:/data/configdb
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - clinicpro-network-dev
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend API Service (development mode with hot reload)
  backend:
    build:
      context: ./Backend - NodeJS
      dockerfile: Dockerfile
      target: builder
    container_name: clinicpro-backend-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3000
      MONGODB_URI: mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-clinicpro123}@mongodb:27017/${MONGO_DATABASE:-clinic-pro}?authSource=admin
      JWT_SECRET: ${JWT_SECRET:-dev-jwt-secret-key}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-7d}
    ports:
      - "${BACKEND_PORT:-3000}:3000"
    volumes:
      - ./Backend - NodeJS/src:/app/src:ro
      - ./Backend - NodeJS/package.json:/app/package.json:ro
      - ./Backend - NodeJS/tsconfig.json:/app/tsconfig.json:ro
      - backend_uploads_dev:/app/uploads
    networks:
      - clinicpro-network-dev
    depends_on:
      mongodb:
        condition: service_healthy
    command: ["npm", "run", "dev"]

  # Frontend Web Application (development mode with hot reload)
  frontend:
    build:
      context: ./Frontend - ReactJS
      dockerfile: Dockerfile
      target: builder
    container_name: clinicpro-frontend-dev
    restart: unless-stopped
    environment:
      VITE_API_BASE_URL: ${VITE_API_BASE_URL:-http://localhost:3000}
    ports:
      - "${FRONTEND_PORT:-5173}:5173"
    volumes:
      - ./Frontend - ReactJS/src:/app/src:ro
      - ./Frontend - ReactJS/public:/app/public:ro
      - ./Frontend - ReactJS/package.json:/app/package.json:ro
      - ./Frontend - ReactJS/vite.config.ts:/app/vite.config.ts:ro
      - ./Frontend - ReactJS/tsconfig.json:/app/tsconfig.json:ro
      - ./Frontend - ReactJS/tailwind.config.ts:/app/tailwind.config.ts:ro
    networks:
      - clinicpro-network-dev
    depends_on:
      - backend
    command: ["npm", "run", "dev", "--", "--host", "0.0.0.0"]

# Named volumes for persistent data (development)
volumes:
  mongodb_data_dev:
    driver: local
  mongodb_config_dev:
    driver: local
  backend_uploads_dev:
    driver: local

# Custom network for service communication (development)
networks:
  clinicpro-network-dev:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16