#!/bin/bash

# ClinicPro Docker Deployment Test Script
# This script tests the Docker deployment to ensure everything is working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Function to print test results
print_test_result() {
    local test_name="$1"
    local result="$2"
    local message="$3"

    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✓ PASS${NC} - $test_name"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}✗ FAIL${NC} - $test_name: $message"
        ((TESTS_FAILED++))
    fi
}

# Function to test HTTP endpoint
test_http_endpoint() {
    local url="$1"
    local expected_status="$2"
    local timeout="${3:-10}"

    local status_code
    status_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout "$timeout" "$url" 2>/dev/null || echo "000")

    if [ "$status_code" = "$expected_status" ]; then
        echo "PASS"
    else
        echo "FAIL:Expected $expected_status, got $status_code"
    fi
}

# Function to test MongoDB connection
test_mongodb_connection() {
    local result
    result=$(docker exec clinicpro-mongodb mongosh --eval "db.adminCommand('ping')" --quiet 2>/dev/null | grep -c "ok.*1" || echo "0")

    if [ "$result" -gt 0 ]; then
        echo "PASS"
    else
        echo "FAIL:MongoDB ping failed"
    fi
}

echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                ClinicPro Deployment Test Suite              ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Test 1: Check if Docker is running
echo -e "${BLUE}[1/10]${NC} Testing Docker availability..."
if docker info &> /dev/null; then
    print_test_result "Docker Service" "PASS"
else
    print_test_result "Docker Service" "FAIL" "Docker is not running"
fi

# Test 2: Check if containers are running
echo -e "${BLUE}[2/10]${NC} Testing container status..."
RUNNING_CONTAINERS=$(docker-compose ps --services --filter "status=running" | wc -l)
if [ "$RUNNING_CONTAINERS" -ge 3 ]; then
    print_test_result "Container Status" "PASS"
else
    print_test_result "Container Status" "FAIL" "Expected 3+ containers, found $RUNNING_CONTAINERS"
fi

# Test 3: Test MongoDB container
echo -e "${BLUE}[3/10]${NC} Testing MongoDB container..."
if docker ps | grep -q "clinicpro-mongodb"; then
    print_test_result "MongoDB Container" "PASS"
else
    print_test_result "MongoDB Container" "FAIL" "MongoDB container not running"
fi

# Test 4: Test Backend container
echo -e "${BLUE}[4/10]${NC} Testing Backend container..."
if docker ps | grep -q "clinicpro-backend"; then
    print_test_result "Backend Container" "PASS"
else
    print_test_result "Backend Container" "FAIL" "Backend container not running"
fi

# Test 5: Test Frontend container
echo -e "${BLUE}[5/10]${NC} Testing Frontend container..."
if docker ps | grep -q "clinicpro-frontend"; then
    print_test_result "Frontend Container" "PASS"
else
    print_test_result "Frontend Container" "FAIL" "Frontend container not running"
fi

# Test 6: Test MongoDB connectivity
echo -e "${BLUE}[6/10]${NC} Testing MongoDB connectivity..."
MONGO_TEST=$(test_mongodb_connection)
if [[ $MONGO_TEST == PASS* ]]; then
    print_test_result "MongoDB Connectivity" "PASS"
else
    print_test_result "MongoDB Connectivity" "FAIL" "${MONGO_TEST#FAIL:}"
fi

# Test 7: Test Backend API health endpoint
echo -e "${BLUE}[7/10]${NC} Testing Backend API health..."
BACKEND_TEST=$(test_http_endpoint "http://localhost:3000/api/health" "200" 15)
if [[ $BACKEND_TEST == PASS* ]]; then
    print_test_result "Backend API Health" "PASS"
else
    print_test_result "Backend API Health" "FAIL" "${BACKEND_TEST#FAIL:}"
fi

# Test 8: Test Frontend accessibility
echo -e "${BLUE}[8/10]${NC} Testing Frontend accessibility..."
FRONTEND_TEST=$(test_http_endpoint "http://localhost:80" "200" 15)
if [[ $FRONTEND_TEST == PASS* ]]; then
    print_test_result "Frontend Accessibility" "PASS"
else
    print_test_result "Frontend Accessibility" "FAIL" "${FRONTEND_TEST#FAIL:}"
fi

# Test 9: Test API endpoints
echo -e "${BLUE}[9/10]${NC} Testing API endpoints..."
API_TEST=$(test_http_endpoint "http://localhost:3000/api" "404" 10)  # 404 is expected for base API route
if [[ $API_TEST == PASS* ]]; then
    print_test_result "API Endpoints" "PASS"
else
    print_test_result "API Endpoints" "FAIL" "${API_TEST#FAIL:}"
fi

# Test 10: Test container health checks
echo -e "${BLUE}[10/10]${NC} Testing container health status..."
HEALTHY_CONTAINERS=$(docker-compose ps | grep -c "healthy" || echo "0")
if [ "$HEALTHY_CONTAINERS" -ge 2 ]; then  # At least backend and frontend should be healthy
    print_test_result "Container Health Checks" "PASS"
else
    print_test_result "Container Health Checks" "FAIL" "Expected 2+ healthy containers, found $HEALTHY_CONTAINERS"
fi

# Summary
echo -e "\n${BLUE}╔══════════════════════════════════════════════════════════════╗"
echo "║                        Test Summary                          ║"
echo "╚══════════════════════════════════════════════════════════════╝${NC}"

echo -e "${GREEN}Tests Passed: $TESTS_PASSED${NC}"
echo -e "${RED}Tests Failed: $TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 ALL TESTS PASSED! 🎉${NC}"
    echo -e "${GREEN}ClinicPro deployment is working correctly.${NC}"
    echo -e "\n${BLUE}Access your application:${NC}"
    echo -e "  Frontend: http://localhost"
    echo -e "  Backend API: http://localhost:3000"
    echo -e "  API Documentation: http://localhost:3000/api-docs"
    exit 0
else
    echo -e "\n${RED}❌ SOME TESTS FAILED ❌${NC}"
    echo -e "${YELLOW}Please check the following:${NC}"
    echo "1. Ensure all containers are running: docker-compose ps"
    echo "2. Check container logs: docker-compose logs"
    echo "3. Verify port availability: netstat -tulpn | grep -E ':(80|3000|27017)'"
    echo "4. Wait a bit longer for services to start up completely"
    echo "5. Try restarting the deployment: docker-compose restart"
    exit 1
fi