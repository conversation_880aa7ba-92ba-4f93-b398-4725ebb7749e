import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ResponsiveTable,
  MobileActionDropdown,
} from "@/components/ui/table";
import {
  ResponsiveHeader,
  ResponsiveStatsCard,
  ResponsiveContainer,
  ResponsiveGrid,
} from "@/components/ui/responsive-container";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  Plus,
  Filter,
  Download,
  MoreVertical,
  FileText,
  Calendar,
  User,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Eye,
  Edit2,
  Printer,
  Send,
  Upload,
  TestTube2,
  Microscope,
  Stethoscope,
  Image as ImageIcon,
  FileImage,
  Loader2,
  AlertCircle,
  RefreshCw,
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import RecordTestReportModal from "@/components/modals/RecordTestReportModal";
import EditTestReportModal from "@/components/modals/EditTestReportModal";
import ViewTestReportModal from "@/components/modals/ViewTestReportModal";
import { TestReport, Test } from "@/types";
import { apiService, Patient } from "@/services/api";

const TestReports = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedVendor, setSelectedVendor] = useState("all");
  const [isRecordModalOpen, setIsRecordModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState<TestReport | null>(null);
  const [viewReportId, setViewReportId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  
  // Data states
  const [testReports, setTestReports] = useState<TestReport[]>([]);
  const [tests, setTests] = useState<Test[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [stats, setStats] = useState({
    totalReports: 0,
    pendingReports: 0,
    recordedReports: 0,
    verifiedReports: 0,
    deliveredReports: 0,
  });
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const pageSize = 10;

  // Extract vendors from test reports
  const vendors = Array.from(new Set((testReports || []).map(report => report.externalVendor).filter(Boolean)));

  // Memoized fetch data function to prevent unnecessary re-renders
  const fetchData = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      setError(null);

      // Fetch test reports with current filters
      const reportsResponse = await apiService.getTestReports({
        page: currentPage,
        limit: pageSize,
        search: searchTerm || undefined,
        status: selectedStatus !== "all" ? selectedStatus : undefined,
        vendor: selectedVendor !== "all" ? selectedVendor : undefined,
        category: selectedCategory !== "all" ? selectedCategory : undefined,
      });

      // Only fetch supporting data on first load or when not initialized
      let testsData = tests;
      let patientsData = patients;
      let statsData = stats;

      if (!isInitialized || tests.length === 0) {
        const [testsResponse, patientsResponse, statsResponse] = await Promise.all([
          apiService.getTests({ limit: 100, is_active: true }),
          apiService.getPatients({ limit: 100 }),
          apiService.getTestReportStats(),
        ]);

        testsData = testsResponse.data?.items || [];
        patientsData = patientsResponse.data?.patients || [];
        statsData = statsResponse || {
          totalReports: 0,
          pendingReports: 0,
          recordedReports: 0,
          verifiedReports: 0,
          deliveredReports: 0,
        };

        setTests(testsData);
        setPatients(patientsData);
        setStats(statsData);
      }

      setTestReports(reportsResponse.data?.items || []);
      setTotalPages(reportsResponse.data?.pagination?.pages || 1);
      setTotalItems(reportsResponse.data?.pagination?.total || 0);
    } catch (err: any) {
      console.error('Error fetching test reports data:', err);
      setError(err.response?.data?.message || 'Failed to fetch test reports data');
      // Reset states to prevent render errors
      setTestReports([]);
      setTests([]);
      setPatients([]);
      setStats({
        totalReports: 0,
        pendingReports: 0,
        recordedReports: 0,
        verifiedReports: 0,
        deliveredReports: 0,
      });
      setTotalPages(1);
      setTotalItems(0);
      toast({
        title: "Error",
        description: "Failed to fetch test reports data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [currentPage, selectedStatus, selectedCategory, selectedVendor, searchTerm, isInitialized, tests.length, patients, stats]);

  // Single effect for data fetching - handles initial load and all changes
  useEffect(() => {
    // Mark as initialized on first run
    if (!isInitialized) {
      setIsInitialized(true);
    }
    
    // Debounce search term changes
    if (searchTerm.trim()) {
      const timeoutId = setTimeout(() => {
        if (currentPage !== 1) {
          setCurrentPage(1);
          return; // Exit early, the currentPage change will trigger this effect again
        }
        fetchData();
      }, 500);

      return () => clearTimeout(timeoutId);
    } else {
      // For non-search changes, fetch immediately
      fetchData();
    }
  }, [currentPage, selectedStatus, selectedCategory, selectedVendor, searchTerm, fetchData, isInitialized]);

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    fetchData(false);
  }, [fetchData]);

  const getPatientName = (patientId: string | Patient) => {
    if (typeof patientId === 'object' && patientId !== null) {
      const patient = patientId as Patient;
      return `${patient.first_name} ${patient.last_name}`;
    }
    const patient = (patients || []).find(p => p._id === patientId);
    return patient ? `${patient.first_name} ${patient.last_name}` : 'Unknown Patient';
  };

  const getTestName = (testId: string | Test) => {
    if (typeof testId === 'object' && testId !== null) {
      const test = testId as Test;
      return test.name;
    }
    const test = (tests || []).find(t => t._id === testId);
    return test?.name || 'Unknown Test';
  };

  const getTestCode = (testId: string | Test) => {
    if (typeof testId === 'object' && testId !== null) {
      const test = testId as Test;
      return test.code;
    }
    const test = (tests || []).find(t => t._id === testId);
    return test?.code || 'N/A';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case "recorded":
        return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case "verified":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "delivered":
        return <Send className="h-4 w-4 text-purple-600" />;
      default:
        return <XCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "recorded":
        return "bg-orange-100 text-orange-800";
      case "verified":
        return "bg-green-100 text-green-800";
      case "delivered":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleViewReport = async (reportId: string) => {
    setViewReportId(reportId);
    setIsViewModalOpen(true);
  };

  const handleEditReport = (report: TestReport) => {
    setSelectedReport(report);
    setIsEditModalOpen(true);
  };

  const handlePrintReport = async (reportId: string) => {
    try {
      // First fetch the report details
      const report = await apiService.getTestReport(reportId);
      
      // Create a print-friendly version
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        const patientName = typeof report.patientId === 'object' 
          ? `${report.patientId.first_name} ${report.patientId.last_name}`
          : report.patientName;
        
        const testName = typeof report.testId === 'object'
          ? report.testId.name
          : report.testName;

        printWindow.document.write(`
          <html>
            <head>
              <title>Test Report - ${report.reportNumber}</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 20px; }
                .section { margin: 15px 0; }
                .label { font-weight: bold; }
                .results { background: #f5f5f5; padding: 10px; margin: 10px 0; }
                table { width: 100%; border-collapse: collapse; }
                td, th { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
              </style>
            </head>
            <body>
              <div class="header">
                <h1>Laboratory Test Report</h1>
                <h2>Report Number: ${report.reportNumber}</h2>
              </div>
              
              <div class="section">
                <h3>Patient Information</h3>
                <table>
                  <tr><td class="label">Name:</td><td>${patientName}</td></tr>
                  <tr><td class="label">Age:</td><td>${report.patientAge} years</td></tr>
                  <tr><td class="label">Gender:</td><td>${report.patientGender}</td></tr>
                </table>
              </div>

              <div class="section">
                <h3>Test Information</h3>
                <table>
                  <tr><td class="label">Test Name:</td><td>${testName}</td></tr>
                  <tr><td class="label">Test Code:</td><td>${report.testCode}</td></tr>
                  <tr><td class="label">Category:</td><td>${report.category}</td></tr>
                  <tr><td class="label">External Vendor:</td><td>${report.externalVendor}</td></tr>
                  <tr><td class="label">Test Date:</td><td>${new Date(report.testDate).toLocaleDateString()}</td></tr>
                  <tr><td class="label">Recorded By:</td><td>${report.recordedBy}</td></tr>
                </table>
              </div>

              ${report.results ? `
                <div class="section">
                  <h3>Test Results</h3>
                  <div class="results">${
                    typeof report.results === 'object' 
                      ? (report.results.value !== undefined && report.results.unit !== undefined 
                          ? `${report.results.value} ${report.results.unit}`
                          : JSON.stringify(report.results, null, 2))
                      : report.results
                  }</div>
                </div>
              ` : ''}

              ${report.interpretation ? `
                <div class="section">
                  <h3>Clinical Interpretation</h3>
                  <div class="results">${report.interpretation}</div>
                </div>
              ` : ''}

              ${report.notes ? `
                <div class="section">
                  <h3>Additional Notes</h3>
                  <div class="results">${report.notes}</div>
                </div>
              ` : ''}

              <div class="section" style="margin-top: 40px; text-align: center; font-size: 12px;">
                <p>Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
              </div>
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }

      toast({
        title: "Print Initiated",
        description: "Report has been sent to printer.",
      });
    } catch (error: any) {
      console.error('Error printing report:', error);
      toast({
        title: "Error",
        description: "Failed to print report. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSendReport = async (reportId: string) => {
    try {
      // Get the report first to get patient email
      const report = await apiService.getTestReport(reportId);
      
      // For now, just copy the view link to clipboard
      const reportUrl = `${window.location.origin}/test-reports/${reportId}`;
      await navigator.clipboard.writeText(reportUrl);
      
      toast({
        title: "Report Link Copied",
        description: "Report link has been copied to clipboard. You can share this with the patient.",
      });

      // TODO: In a real implementation, you would:
      // 1. Send email to patient with the report
      // 2. Send SMS notification
      // 3. Update report status to delivered
      // await apiService.sendTestReportToPatient(reportId);
      
    } catch (error: any) {
      console.error('Error sending report:', error);
      toast({
        title: "Error",
        description: "Failed to send report. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleVerifyReport = useCallback(async (reportId: string) => {
    try {
      await apiService.verifyTestReport(reportId);
      toast({
        title: "Success",
        description: "Test report verified successfully.",
      });
      fetchData(false);
    } catch (err: any) {
      console.error('Error verifying test report:', err);
      toast({
        title: "Error",
        description: err.response?.data?.message || "Failed to verify test report.",
        variant: "destructive",
      });
    }
  }, [fetchData]);

  const handleDeliverReport = useCallback(async (reportId: string) => {
    try {
      await apiService.deliverTestReport(reportId);
      toast({
        title: "Success",
        description: "Test report delivered successfully.",
      });
      fetchData(false);
    } catch (err: any) {
      console.error('Error delivering test report:', err);
      toast({
        title: "Error",
        description: err.response?.data?.message || "Failed to deliver test report.",
        variant: "destructive",
      });
    }
  }, [fetchData]);

  const handleDeleteReport = useCallback(async (reportId: string) => {
    if (!confirm("Are you sure you want to delete this test report?")) return;

    try {
      await apiService.deleteTestReport(reportId);
      toast({
        title: "Success",
        description: "Test report deleted successfully.",
      });
      fetchData(false);
    } catch (err: any) {
      console.error('Error deleting test report:', err);
      toast({
        title: "Error",
        description: err.response?.data?.message || "Failed to delete test report.",
        variant: "destructive",
      });
    }
  }, [fetchData]);

  const handleRecordReport = () => {
    setIsRecordModalOpen(true);
  };

  const handleReportRecorded = useCallback(() => {
    setIsRecordModalOpen(false);
    fetchData(false);
  }, [fetchData]);

  const handleReportUpdated = useCallback(() => {
    setIsEditModalOpen(false);
    setSelectedReport(null);
    fetchData(false);
  }, [fetchData]);

  // Table column configuration
  const columns = [
    {
      key: "reportNumber",
      label: "Report Details",
      render: (report: TestReport) => (
        <div>
          <div className="font-medium">{report.reportNumber}</div>
          <div className="text-sm text-muted-foreground">
            Recorded: {report.recordedDate ? formatDate(report.recordedDate) : 'Not recorded'}
          </div>
        </div>
      ),
    },
    {
      key: "patientId",
      label: "Patient",
      render: (report: TestReport) => (
        <div>
          <div className="font-medium">{getPatientName(report.patientId)}</div>
          <div className="text-sm text-muted-foreground flex items-center space-x-1">
            <User className="h-3 w-3" />
            <span>Patient</span>
          </div>
        </div>
      ),
    },
    {
      key: "testId",
      label: "Test",
      render: (report: TestReport) => (
        <div>
          <div className="font-medium">{getTestName(report.testId)}</div>
          <div className="text-sm text-muted-foreground">
            Code: {getTestCode(report.testId)}
          </div>
        </div>
      ),
    },
    {
      key: "externalVendor",
      label: "Vendor",
      render: (report: TestReport) => (
        <span className="text-sm">
          {report.externalVendor || 'Internal'}
        </span>
      ),
    },
    {
      key: "testDate",
      label: "Test Date",
      render: (report: TestReport) => (
        <div className="flex items-center space-x-1">
          <Calendar className="h-3 w-3 text-muted-foreground" />
          <span className="text-sm">{formatDate(report.testDate)}</span>
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (report: TestReport) => (
        <div className="flex items-center space-x-2">
          {getStatusIcon(report.status)}
          <Badge className={getStatusColor(report.status)}>
            {report.status.replace('_', ' ').toUpperCase()}
          </Badge>
        </div>
      ),
    },
    {
      key: "attachments",
      label: "Attachments",
      render: (report: TestReport) => (
        report.attachments && report.attachments.length > 0 ? (
          <div className="flex items-center space-x-1">
            <FileImage className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{report.attachments.length}</span>
          </div>
        ) : (
          <span className="text-sm text-muted-foreground">None</span>
        )
      ),
    },
  ];

  // Mobile card configuration
  const mobileCard = {
    title: (report: TestReport) => (
      <div className="flex items-start justify-between">
        <div>
          <div className="font-semibold text-base">{report.reportNumber}</div>
          <div className="text-sm text-muted-foreground mt-1">
            {getPatientName(report.patientId)}
          </div>
        </div>
        <div className="flex items-center space-x-2 ml-4">
          {getStatusIcon(report.status)}
          <Badge className={`${getStatusColor(report.status)} text-xs`}>
            {report.status.replace('_', ' ').toUpperCase()}
          </Badge>
        </div>
      </div>
    ),
    content: (report: TestReport) => (
      <div className="space-y-3 mt-3">
        <div className="grid grid-cols-1 gap-2">
          <div className="flex items-center space-x-2 text-sm">
            <TestTube2 className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            <span className="font-medium">{getTestName(report.testId)}</span>
            <span className="text-muted-foreground">({getTestCode(report.testId)})</span>
          </div>
          
          <div className="flex items-center space-x-2 text-sm">
            <Calendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            <span>Test Date: {formatDate(report.testDate)}</span>
          </div>

          {report.externalVendor && (
            <div className="flex items-center space-x-2 text-sm">
              <Microscope className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span>Vendor: {report.externalVendor}</span>
            </div>
          )}

          {report.attachments && report.attachments.length > 0 && (
            <div className="flex items-center space-x-2 text-sm">
              <FileImage className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span>{report.attachments.length} attachment(s)</span>
            </div>
          )}

          {report.recordedDate && (
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4 flex-shrink-0" />
              <span>Recorded: {formatDate(report.recordedDate)}</span>
            </div>
          )}
        </div>
      </div>
    ),
    actions: (report: TestReport) => {
      const actions = [
        {
          label: "View Report",
          onClick: () => handleViewReport(report._id),
          icon: Eye,
        },
        {
          label: "Edit Report",
          onClick: () => handleEditReport(report),
          icon: Edit2,
        },
        {
          label: "Print Report",
          onClick: () => handlePrintReport(report._id),
          icon: Printer,
        },
        {
          label: "Send to Patient",
          onClick: () => handleSendReport(report._id),
          icon: Send,
        },
      ];

      if (report.status === 'recorded') {
        actions.push({
          label: "Verify Report",
          onClick: () => handleVerifyReport(report._id),
          icon: CheckCircle,
        });
      }

      if (report.status === 'verified') {
        actions.push({
          label: "Mark as Delivered",
          onClick: () => handleDeliverReport(report._id),
          icon: Send,
        });
      }

      actions.push({
        label: "Delete Report",
        onClick: () => handleDeleteReport(report._id),
        icon: XCircle,
      });

      return (
        <MobileActionDropdown
          actions={actions}
        />
      );
    },
  };

  if (loading) {
    return (
      <ResponsiveContainer>
        <div className="flex items-center justify-center h-96">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading test reports...</span>
          </div>
        </div>
      </ResponsiveContainer>
    );
  }

  if (error) {
    return (
      <ResponsiveContainer>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Error Loading Test Reports</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => fetchData()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </div>
      </ResponsiveContainer>
    );
  }

  return (
    <ResponsiveContainer className="space-y-6">
      {/* Header */}
      <ResponsiveHeader
        title="Test Reports"
        subtitle="Manage laboratory test reports and results"
        actions={
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <RecordTestReportModal 
              trigger={
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Record Report
                </Button>
              }
              onReportRecorded={handleReportRecorded}
            />
          </div>
        }
      />

      {/* Stats Cards */}
      <ResponsiveGrid columns={4} className="gap-4">
        <ResponsiveStatsCard
          title="Total Reports"
          value={stats.totalReports}
          icon={FileText}
          subtitle={`${stats.verifiedReports} verified`}
        />
        <ResponsiveStatsCard
          title="Pending"
          value={stats.pendingReports}
          icon={Clock}
          subtitle="Awaiting processing"
        />
        <ResponsiveStatsCard
          title="Recorded"
          value={stats.recordedReports}
          icon={AlertTriangle}
          subtitle="Being processed"
        />
        <ResponsiveStatsCard
          title="Delivered"
          value={stats.deliveredReports}
          icon={CheckCircle}
          subtitle="Completed reports"
        />
      </ResponsiveGrid>

      {/* Filters */}
      <Card>
        <CardContent className="form-responsive">
          <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search reports..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-full sm:w-[150px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="recorded">Recorded</SelectItem>
                  <SelectItem value="verified">Verified</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Reports Table */}
      <Card>
        <CardHeader>
          <CardTitle>Test Reports ({totalItems})</CardTitle>
          <CardDescription>
            A list of all laboratory test reports and their status.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <ResponsiveTable
            data={testReports || []}
            columns={columns}
            mobileCard={mobileCard}
            loading={loading}
            emptyMessage={
              searchTerm || selectedStatus !== "all" || selectedVendor !== "all" 
                ? "No test reports found. Try adjusting your filters." 
                : "No test reports found. Record your first test report to get started."
            }
          />
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div className="text-sm text-muted-foreground text-center sm:text-left">
            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalItems)} of {totalItems} reports
          </div>
          <div className="flex items-center justify-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <div className="text-sm px-2">
              Page {currentPage} of {totalPages}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Modals */}
      {selectedReport && (
        <EditTestReportModal
          report={selectedReport}
          open={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedReport(null);
          }}
          onReportUpdated={handleReportUpdated}
        />
      )}

      <ViewTestReportModal
        reportId={viewReportId}
        open={isViewModalOpen}
        onClose={() => {
          setIsViewModalOpen(false);
          setViewReportId(null);
        }}
      />
    </ResponsiveContainer>
  );
};

export default TestReports;
