#!/bin/bash

# ClinicPro Quick Start Script
# This script sets up and deploys ClinicPro with minimal user interaction

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    ClinicPro Quick Start                     ║"
echo "║              Clinic Management System                        ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Check if Docker is installed and running
echo -e "${BLUE}[1/5]${NC} Checking Docker installation..."
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Error: Docker is not installed. Please install Docker first.${NC}"
    echo "Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo -e "${RED}Error: Docker is not running. Please start Docker and try again.${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Docker is installed and running${NC}"

# Check if docker-compose is available
echo -e "${BLUE}[2/5]${NC} Checking Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Error: Docker Compose is not installed.${NC}"
    echo "Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

echo -e "${GREEN}✓ Docker Compose is available${NC}"

# Set up environment file
echo -e "${BLUE}[3/5]${NC} Setting up environment configuration..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo -e "${GREEN}✓ Environment file created from template${NC}"
    echo -e "${YELLOW}Note: Review .env file for production deployment${NC}"
else
    echo -e "${GREEN}✓ Environment file already exists${NC}"
fi

# Ask user for deployment type
echo -e "${BLUE}[4/5]${NC} Choose deployment type:"
echo "1) Production (recommended for live use)"
echo "2) Development (for testing and development)"
echo -n "Enter your choice (1 or 2): "
read -r choice

case $choice in
    1)
        DEPLOY_TYPE="production"
        COMPOSE_FILE="docker-compose.yml"
        FRONTEND_URL="http://localhost"
        ;;
    2)
        DEPLOY_TYPE="development"
        COMPOSE_FILE="docker-compose.dev.yml"
        FRONTEND_URL="http://localhost:5173"
        ;;
    *)
        echo -e "${RED}Invalid choice. Defaulting to production.${NC}"
        DEPLOY_TYPE="production"
        COMPOSE_FILE="docker-compose.yml"
        FRONTEND_URL="http://localhost"
        ;;
esac

# Deploy the application
echo -e "${BLUE}[5/5]${NC} Deploying ClinicPro in ${DEPLOY_TYPE} mode..."
echo "This may take a few minutes for the first deployment..."

if [ "$DEPLOY_TYPE" = "development" ]; then
    docker-compose -f docker-compose.dev.yml down --remove-orphans 2>/dev/null || true
    docker-compose -f docker-compose.dev.yml up --build -d
else
    docker-compose down --remove-orphans 2>/dev/null || true
    docker-compose up --build -d
fi

# Wait for services to be ready
echo -e "${BLUE}Waiting for services to start...${NC}"
sleep 30

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                 🎉 DEPLOYMENT SUCCESSFUL! 🎉                 ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo -e "${GREEN}ClinicPro is now running!${NC}"
    echo ""
    echo -e "${BLUE}Access URLs:${NC}"
    echo -e "  Frontend:    ${FRONTEND_URL}"
    echo -e "  Backend API: http://localhost:3000"
    echo -e "  MongoDB:     localhost:27017"
    echo ""
    echo -e "${BLUE}Useful Commands:${NC}"
    echo -e "  View logs:   docker-compose logs -f"
    echo -e "  Stop app:    docker-compose down"
    echo -e "  Restart:     docker-compose restart"
    echo ""
    echo -e "${YELLOW}Next Steps:${NC}"
    echo "1. Open ${FRONTEND_URL} in your browser"
    echo "2. Create your admin account"
    echo "3. Configure clinic settings"
    echo "4. Start managing your clinic!"
    echo ""
    echo -e "${YELLOW}For production deployment:${NC}"
    echo "- Review and update .env file with secure passwords"
    echo "- Configure HTTPS/SSL certificates"
    echo "- Set up regular backups"
    echo ""
else
    echo -e "${RED}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                   ❌ DEPLOYMENT FAILED ❌                    ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo -e "${RED}Something went wrong during deployment.${NC}"
    echo ""
    echo -e "${BLUE}Troubleshooting steps:${NC}"
    echo "1. Check logs: docker-compose logs"
    echo "2. Verify Docker is running: docker info"
    echo "3. Check port availability: netstat -tulpn | grep :80"
    echo "4. Try manual deployment: docker-compose up --build"
    echo ""
    echo "For more help, check README-DOCKER.md"
    exit 1
fi