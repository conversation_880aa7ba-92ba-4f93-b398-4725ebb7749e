#!/bin/bash

# ClinicPro Quick Start Script
# This script sets up and deploys ClinicPro with minimal user interaction

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Function to check if port is available
check_port() {
    local port=$1
    if command -v netstat >/dev/null 2>&1; then
        ! netstat -tuln | grep -q ":$port "
    elif command -v ss >/dev/null 2>&1; then
        ! ss -tuln | grep -q ":$port "
    else
        # Fallback: try to bind to the port
        ! timeout 1 bash -c "</dev/tcp/127.0.0.1/$port" 2>/dev/null
    fi
}

# Function to find available port starting from a base port
find_available_port() {
    local base_port=$1
    local max_attempts=${2:-50}
    local port=$base_port

    for ((i=0; i<max_attempts; i++)); do
        if check_port $port; then
            echo $port
            return 0
        fi
        ((port++))
    done

    echo "0"  # No available port found
    return 1
}

# Function to find available subnet
find_available_subnet() {
    local base_subnet="172"
    local third_octet

    for third_octet in {25..30} {22..24} {31..50}; do
        local subnet="${base_subnet}.${third_octet}.0.0/16"
        # Check if subnet is in use by existing Docker networks
        if ! docker network ls --format "{{.Name}}" | xargs -I {} docker network inspect {} 2>/dev/null | grep -q "${base_subnet}.${third_octet}"; then
            echo "$subnet"
            return 0
        fi
    done

    # Fallback to a random subnet
    local random_third=$((RANDOM % 50 + 100))
    echo "${base_subnet}.${random_third}.0.0/16"
}

# Function to cleanup existing deployment
cleanup_existing() {
    echo -e "${YELLOW}Cleaning up existing deployment...${NC}"

    # Try both docker-compose and docker compose commands
    if command -v docker-compose >/dev/null 2>&1; then
        docker-compose down --remove-orphans 2>/dev/null || true
    fi

    if command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
        docker compose down --remove-orphans 2>/dev/null || true
    fi

    # Remove any conflicting networks
    docker network ls --format "{{.Name}}" | grep -E "clinicpro|hospitalapp" | xargs -r docker network rm 2>/dev/null || true
}

echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    ClinicPro Quick Start                     ║"
echo "║              Clinic Management System                        ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Check if Docker is installed and running
echo -e "${BLUE}[1/5]${NC} Checking Docker installation..."
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Error: Docker is not installed. Please install Docker first.${NC}"
    echo "Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo -e "${RED}Error: Docker is not running. Please start Docker and try again.${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Docker is installed and running${NC}"

# Check if docker-compose is available
echo -e "${BLUE}[2/5]${NC} Checking Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Error: Docker Compose is not installed.${NC}"
    echo "Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

echo -e "${GREEN}✓ Docker Compose is available${NC}"

# Set up environment file and auto-configure ports
echo -e "${BLUE}[3/6]${NC} Setting up environment configuration..."

# Clean up any existing deployment first
cleanup_existing

# Find available ports
echo -e "${BLUE}Finding available ports...${NC}"
FRONTEND_PORT=$(find_available_port 80)
if [ "$FRONTEND_PORT" = "0" ]; then
    FRONTEND_PORT=$(find_available_port 8080)
fi

BACKEND_PORT=$(find_available_port 3000)
if [ "$BACKEND_PORT" = "0" ]; then
    BACKEND_PORT=$(find_available_port 3001)
fi

MONGO_PORT=$(find_available_port 27017)
if [ "$MONGO_PORT" = "0" ]; then
    MONGO_PORT=$(find_available_port 27018)
fi

# Find available subnet
SUBNET=$(find_available_subnet)

echo -e "${GREEN}✓ Auto-configured ports:${NC}"
echo -e "  Frontend: $FRONTEND_PORT"
echo -e "  Backend:  $BACKEND_PORT"
echo -e "  MongoDB:  $MONGO_PORT"
echo -e "  Network:  $SUBNET"

# Create or update .env file
if [ ! -f .env ]; then
    cp .env.example .env
    echo -e "${GREEN}✓ Environment file created from template${NC}"
else
    echo -e "${GREEN}✓ Environment file already exists${NC}"
fi

# Update ports in .env file
sed -i.bak "s/^FRONTEND_PORT=.*/FRONTEND_PORT=$FRONTEND_PORT/" .env 2>/dev/null || \
    echo "FRONTEND_PORT=$FRONTEND_PORT" >> .env

sed -i.bak "s/^BACKEND_PORT=.*/BACKEND_PORT=$BACKEND_PORT/" .env 2>/dev/null || \
    echo "BACKEND_PORT=$BACKEND_PORT" >> .env

sed -i.bak "s/^MONGO_PORT=.*/MONGO_PORT=$MONGO_PORT/" .env 2>/dev/null || \
    echo "MONGO_PORT=$MONGO_PORT" >> .env

# Update docker-compose.yml with available subnet
sed -i.bak "s|subnet: .*|subnet: $SUBNET|" docker-compose.yml 2>/dev/null || true
sed -i.bak "s|subnet: .*|subnet: $SUBNET|" docker-compose.dev.yml 2>/dev/null || true

# Ask user for deployment type
echo -e "${BLUE}[4/6]${NC} Choose deployment type:"
echo "1) Production (recommended for live use)"
echo "2) Development (for testing and development)"
echo -n "Enter your choice (1 or 2): "
read -r choice

case $choice in
    1)
        DEPLOY_TYPE="production"
        COMPOSE_FILE="docker-compose.yml"
        FRONTEND_URL="http://localhost:$FRONTEND_PORT"
        ;;
    2)
        DEPLOY_TYPE="development"
        COMPOSE_FILE="docker-compose.dev.yml"
        # Development mode uses different port configuration
        DEV_FRONTEND_PORT=$(find_available_port 5173)
        FRONTEND_URL="http://localhost:$DEV_FRONTEND_PORT"
        sed -i.bak "s/5173:5173/$DEV_FRONTEND_PORT:5173/" docker-compose.dev.yml 2>/dev/null || true
        ;;
    *)
        echo -e "${RED}Invalid choice. Defaulting to production.${NC}"
        DEPLOY_TYPE="production"
        COMPOSE_FILE="docker-compose.yml"
        FRONTEND_URL="http://localhost:$FRONTEND_PORT"
        ;;
esac

# Deploy the application
echo -e "${BLUE}[5/6]${NC} Deploying ClinicPro in ${DEPLOY_TYPE} mode..."
echo "This may take a few minutes for the first deployment..."

# Use the appropriate Docker Compose command
DOCKER_COMPOSE_CMD=""
if command -v docker-compose >/dev/null 2>&1; then
    DOCKER_COMPOSE_CMD="docker-compose"
elif command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
    DOCKER_COMPOSE_CMD="docker compose"
else
    echo -e "${RED}Error: Neither docker-compose nor docker compose found${NC}"
    exit 1
fi

if [ "$DEPLOY_TYPE" = "development" ]; then
    $DOCKER_COMPOSE_CMD -f docker-compose.dev.yml up --build -d
else
    $DOCKER_COMPOSE_CMD up --build -d
fi

# Wait for services to be ready
echo -e "${BLUE}[6/6]${NC} Waiting for services to start..."
sleep 30

# Verify deployment
echo -e "${BLUE}Verifying deployment...${NC}"

# Check if services are running
SERVICES_RUNNING=false
if $DOCKER_COMPOSE_CMD ps | grep -q "Up" 2>/dev/null; then
    SERVICES_RUNNING=true
elif [ "$DEPLOY_TYPE" = "development" ] && $DOCKER_COMPOSE_CMD -f docker-compose.dev.yml ps | grep -q "Up" 2>/dev/null; then
    SERVICES_RUNNING=true
fi

if $SERVICES_RUNNING; then
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                 🎉 DEPLOYMENT SUCCESSFUL! 🎉                 ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo -e "${GREEN}ClinicPro is now running!${NC}"
    echo ""
    echo -e "${BLUE}Access URLs:${NC}"
    echo -e "  Frontend:    ${FRONTEND_URL}"
    echo -e "  Backend API: http://localhost:${BACKEND_PORT}"
    echo -e "  MongoDB:     localhost:${MONGO_PORT}"
    echo ""
    echo -e "${BLUE}Useful Commands:${NC}"
    echo -e "  View logs:   $DOCKER_COMPOSE_CMD logs -f"
    echo -e "  Stop app:    $DOCKER_COMPOSE_CMD down"
    echo -e "  Restart:     $DOCKER_COMPOSE_CMD restart"
    echo ""
    echo -e "${YELLOW}Next Steps:${NC}"
    echo "1. Open ${FRONTEND_URL} in your browser"
    echo "2. Create your admin account"
    echo "3. Configure clinic settings"
    echo "4. Start managing your clinic!"
    echo ""
    echo -e "${YELLOW}For production deployment:${NC}"
    echo "- Review and update .env file with secure passwords"
    echo "- Configure HTTPS/SSL certificates"
    echo "- Set up regular backups"
    echo ""
else
    echo -e "${RED}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                   ❌ DEPLOYMENT FAILED ❌                    ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo -e "${RED}Something went wrong during deployment.${NC}"
    echo ""
    echo -e "${BLUE}Troubleshooting steps:${NC}"
    echo "1. Check logs: $DOCKER_COMPOSE_CMD logs"
    echo "2. Verify Docker is running: docker info"
    echo "3. Check port availability: netstat -tulpn | grep -E ':(${FRONTEND_PORT}|${BACKEND_PORT}|${MONGO_PORT})'"
    echo "4. Try manual deployment: $DOCKER_COMPOSE_CMD up --build"
    echo "5. Clean restart: $DOCKER_COMPOSE_CMD down && $DOCKER_COMPOSE_CMD up --build"
    echo ""
    echo "For more help, check README-DOCKER.md"
    exit 1
fi