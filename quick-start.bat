@echo off
setlocal enabledelayedexpansion

REM ClinicPro Quick Start Script for Windows
REM This script sets up and deploys ClinicPro with minimal user interaction

echo.
echo ================================================================
echo                    ClinicPro Quick Start
echo              Clinic Management System
echo ================================================================
echo.

REM Function to check if port is available
:check_port
set port=%1
netstat -an | findstr ":%port% " >nul
if errorlevel 1 (
    exit /b 0
) else (
    exit /b 1
)

REM Function to find available port
:find_available_port
set base_port=%1
set port=%base_port%
set /a max_port=%base_port%+50

:port_loop
call :check_port %port%
if errorlevel 1 (
    set /a port+=1
    if !port! leq %max_port% goto port_loop
    set port=0
)
exit /b 0

REM Cleanup existing deployment
echo [0/6] Cleaning up existing deployment...
docker-compose down --remove-orphans >nul 2>&1
docker compose down --remove-orphans >nul 2>&1

REM Check if Docker is installed and running
echo [1/5] Checking Docker installation...
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker and try again.
    echo Visit: https://docs.docker.com/get-docker/
    pause
    exit /b 1
)
echo [SUCCESS] Docker is installed and running

REM Check if docker-compose is available
echo [2/5] Checking Docker Compose...
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose is not installed.
    echo Visit: https://docs.docker.com/compose/install/
    pause
    exit /b 1
)
echo [SUCCESS] Docker Compose is available

REM Set up environment file and auto-configure ports
echo [3/6] Setting up environment configuration...

REM Find available ports
echo Finding available ports...
call :find_available_port 80
set FRONTEND_PORT=%port%
if %FRONTEND_PORT%==0 (
    call :find_available_port 8080
    set FRONTEND_PORT=%port%
)

call :find_available_port 3000
set BACKEND_PORT=%port%
if %BACKEND_PORT%==0 (
    call :find_available_port 3001
    set BACKEND_PORT=%port%
)

call :find_available_port 27017
set MONGO_PORT=%port%
if %MONGO_PORT%==0 (
    call :find_available_port 27018
    set MONGO_PORT=%port%
)

echo [SUCCESS] Auto-configured ports:
echo   Frontend: %FRONTEND_PORT%
echo   Backend:  %BACKEND_PORT%
echo   MongoDB:  %MONGO_PORT%

REM Create or update .env file
if not exist .env (
    copy .env.example .env >nul
    echo [SUCCESS] Environment file created from template
) else (
    echo [SUCCESS] Environment file already exists
)

REM Update ports in .env file
powershell -Command "(Get-Content .env) -replace '^FRONTEND_PORT=.*', 'FRONTEND_PORT=%FRONTEND_PORT%' | Set-Content .env" 2>nul
powershell -Command "(Get-Content .env) -replace '^BACKEND_PORT=.*', 'BACKEND_PORT=%BACKEND_PORT%' | Set-Content .env" 2>nul
powershell -Command "(Get-Content .env) -replace '^MONGO_PORT=.*', 'MONGO_PORT=%MONGO_PORT%' | Set-Content .env" 2>nul

REM Add ports if they don't exist
echo FRONTEND_PORT=%FRONTEND_PORT% >> .env 2>nul
echo BACKEND_PORT=%BACKEND_PORT% >> .env 2>nul
echo MONGO_PORT=%MONGO_PORT% >> .env 2>nul

REM Ask user for deployment type
echo [4/5] Choose deployment type:
echo 1^) Production ^(recommended for live use^)
echo 2^) Development ^(for testing and development^)
set /p choice="Enter your choice (1 or 2): "

if "%choice%"=="1" (
    set DEPLOY_TYPE=production
    set COMPOSE_FILE=docker-compose.yml
    set FRONTEND_URL=http://localhost
) else if "%choice%"=="2" (
    set DEPLOY_TYPE=development
    set COMPOSE_FILE=docker-compose.dev.yml
    set FRONTEND_URL=http://localhost:5173
) else (
    echo [WARNING] Invalid choice. Defaulting to production.
    set DEPLOY_TYPE=production
    set COMPOSE_FILE=docker-compose.yml
    set FRONTEND_URL=http://localhost
)

REM Deploy the application
echo [5/5] Deploying ClinicPro in %DEPLOY_TYPE% mode...
echo This may take a few minutes for the first deployment...

if "%DEPLOY_TYPE%"=="development" (
    docker-compose -f docker-compose.dev.yml down --remove-orphans >nul 2>&1
    docker-compose -f docker-compose.dev.yml up --build -d
) else (
    docker-compose down --remove-orphans >nul 2>&1
    docker-compose up --build -d
)

REM Wait for services to be ready
echo Waiting for services to start...
timeout /t 30 /nobreak >nul

REM Check if services are running
docker-compose ps | findstr "Up" >nul
if not errorlevel 1 (
    echo.
    echo ================================================================
    echo                 DEPLOYMENT SUCCESSFUL!
    echo ================================================================
    echo.
    echo ClinicPro is now running!
    echo.
    echo Access URLs:
    echo   Frontend:    %FRONTEND_URL%
    echo   Backend API: http://localhost:3000
    echo   MongoDB:     localhost:27017
    echo.
    echo Useful Commands:
    echo   View logs:   docker-compose logs -f
    echo   Stop app:    docker-compose down
    echo   Restart:     docker-compose restart
    echo.
    echo Next Steps:
    echo 1. Open %FRONTEND_URL% in your browser
    echo 2. Create your admin account
    echo 3. Configure clinic settings
    echo 4. Start managing your clinic!
    echo.
    echo For production deployment:
    echo - Review and update .env file with secure passwords
    echo - Configure HTTPS/SSL certificates
    echo - Set up regular backups
    echo.
) else (
    echo.
    echo ================================================================
    echo                   DEPLOYMENT FAILED
    echo ================================================================
    echo.
    echo Something went wrong during deployment.
    echo.
    echo Troubleshooting steps:
    echo 1. Check logs: docker-compose logs
    echo 2. Verify Docker is running: docker info
    echo 3. Check port availability
    echo 4. Try manual deployment: docker-compose up --build
    echo.
    echo For more help, check README-DOCKER.md
    pause
    exit /b 1
)

pause