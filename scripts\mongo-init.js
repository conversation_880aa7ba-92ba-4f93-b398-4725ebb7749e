// MongoDB Initialization Script for ClinicPro
// This script runs when the MongoDB container starts for the first time

// Get database name from environment variable or use default
const dbName = process.env.MONGO_INITDB_DATABASE || 'clinic-pro';

// Switch to the application database
db = db.getSiblingDB(dbName);

// Create application user with read/write permissions
db.createUser({
  user: 'clinicpro-app',
  pwd: 'clinicpro-app-password',
  roles: [
    {
      role: 'readWrite',
      db: dbName
    }
  ]
});

// Create indexes for better performance
print('Creating indexes for ClinicPro collections...');

// Users collection indexes
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ role: 1 });
db.users.createIndex({ isActive: 1 });
db.users.createIndex({ createdAt: -1 });

// Patients collection indexes
db.patients.createIndex({ patientId: 1 }, { unique: true });
db.patients.createIndex({ email: 1 });
db.patients.createIndex({ phone: 1 });
db.patients.createIndex({ firstName: 1, lastName: 1 });
db.patients.createIndex({ createdAt: -1 });

// Appointments collection indexes
db.appointments.createIndex({ patientId: 1 });
db.appointments.createIndex({ doctorId: 1 });
db.appointments.createIndex({ appointmentDate: 1 });
db.appointments.createIndex({ status: 1 });
db.appointments.createIndex({ createdAt: -1 });

// Medical records collection indexes
db.medicalrecords.createIndex({ patientId: 1 });
db.medicalrecords.createIndex({ doctorId: 1 });
db.medicalrecords.createIndex({ visitDate: -1 });
db.medicalrecords.createIndex({ createdAt: -1 });

// Prescriptions collection indexes
db.prescriptions.createIndex({ patientId: 1 });
db.prescriptions.createIndex({ doctorId: 1 });
db.prescriptions.createIndex({ prescriptionDate: -1 });
db.prescriptions.createIndex({ status: 1 });

// Laboratory collection indexes
db.labtests.createIndex({ patientId: 1 });
db.labtests.createIndex({ testDate: -1 });
db.labtests.createIndex({ status: 1 });
db.labtests.createIndex({ testType: 1 });

// Billing collection indexes
db.bills.createIndex({ patientId: 1 });
db.bills.createIndex({ billDate: -1 });
db.bills.createIndex({ status: 1 });
db.bills.createIndex({ totalAmount: 1 });

// Inventory collection indexes
db.inventory.createIndex({ itemName: 1 });
db.inventory.createIndex({ category: 1 });
db.inventory.createIndex({ quantity: 1 });
db.inventory.createIndex({ expiryDate: 1 });

print('Database initialization completed successfully!');
print('Database: ' + dbName);
print('Application user created: clinicpro-app');
print('Indexes created for all major collections');

// Optional: Insert some initial data
print('Inserting initial configuration data...');

// Insert default roles if they don't exist
const roles = [
  { name: 'admin', description: 'System Administrator' },
  { name: 'doctor', description: 'Medical Doctor' },
  { name: 'nurse', description: 'Nurse' },
  { name: 'receptionist', description: 'Receptionist' },
  { name: 'accountant', description: 'Accountant' },
  { name: 'lab_technician', description: 'Laboratory Technician' }
];

roles.forEach(role => {
  db.roles.updateOne(
    { name: role.name },
    { $setOnInsert: role },
    { upsert: true }
  );
});

// Insert default settings
const defaultSettings = {
  clinicName: 'ClinicPro Medical Center',
  clinicAddress: '123 Healthcare Street, Medical City',
  clinicPhone: '******-0123',
  clinicEmail: '<EMAIL>',
  currency: 'USD',
  timezone: 'UTC',
  appointmentDuration: 30, // minutes
  workingHours: {
    start: '09:00',
    end: '17:00'
  },
  workingDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
  createdAt: new Date(),
  updatedAt: new Date()
};

db.settings.updateOne(
  { _id: 'clinic-settings' },
  { $setOnInsert: defaultSettings },
  { upsert: true }
);

print('Initial configuration data inserted successfully!');
print('MongoDB initialization script completed.');