# ClinicPro Docker Deployment Guide

This guide provides comprehensive instructions for deploying the ClinicPro clinic management system using Docker containers with a single command.

## 🚀 Quick Start

### Prerequisites

Before deploying ClinicPro, ensure you have the following installed:

- **Docker** (version 20.10 or higher)
- **Docker Compose** (version 2.0 or higher)
- **Git** (for cloning the repository)

### Installation Steps

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd clinicpro
   ```

2. **Set up environment variables**:
   ```bash
   # Copy the example environment file
   cp .env.example .env

   # Edit the .env file with your preferred settings
   nano .env  # or use your preferred editor
   ```

3. **Deploy with a single command**:

   **For Production:**
   ```bash
   # Linux/macOS
   ./scripts/deploy.sh production

   # Windows
   scripts\deploy.bat production
   ```

   **For Development:**
   ```bash
   # Linux/macOS
   ./scripts/deploy.sh development

   # Windows
   scripts\deploy.bat development
   ```

4. **Access the application**:
   - **Frontend**: http://localhost (production) or http://localhost:5173 (development)
   - **Backend API**: http://localhost:3000
   - **MongoDB**: localhost:27017

## 📋 System Architecture

The Docker deployment consists of three main services:

### 🗄️ MongoDB Database
- **Image**: mongo:7.0
- **Purpose**: Primary database for storing all application data
- **Features**:
  - Automatic initialization with indexes
  - Health checks
  - Persistent data storage
  - Default admin user creation

### 🔧 Backend API Service
- **Technology**: Node.js/TypeScript with Express.js
- **Purpose**: RESTful API server
- **Features**:
  - Multi-stage Docker build for optimization
  - Health checks and monitoring
  - File upload support
  - JWT authentication
  - Rate limiting and security headers

### 🌐 Frontend Web Application
- **Technology**: React/TypeScript with Vite
- **Purpose**: User interface
- **Features**:
  - Nginx reverse proxy
  - Static asset optimization
  - Client-side routing support
  - API proxy configuration
  - Security headers

## ⚙️ Configuration

### Environment Variables

The system uses environment variables for configuration. Key variables include:

| Variable | Default | Description |
|----------|---------|-------------|
| `FRONTEND_PORT` | 80 | Port for frontend access |
| `BACKEND_PORT` | 3000 | Port for backend API |
| `MONGO_PORT` | 27017 | Port for MongoDB |
| `MONGO_ROOT_USERNAME` | admin | MongoDB admin username |
| `MONGO_ROOT_PASSWORD` | clinicpro123 | MongoDB admin password |
| `MONGO_DATABASE` | clinic-pro | Database name |
| `JWT_SECRET` | (required) | JWT signing secret |
| `JWT_EXPIRES_IN` | 7d | JWT token expiration |

### Security Configuration

**⚠️ IMPORTANT SECURITY NOTES:**

1. **Change default passwords** before production deployment
2. **Use strong JWT secrets** (minimum 32 characters)
3. **Configure HTTPS** in production environments
4. **Regularly update credentials**
5. **Use Docker secrets** for sensitive data in production

### AWS S3 Configuration (Optional)

For file uploads using AWS S3:

```env
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-bucket-name
```

## 🛠️ Deployment Commands

### Using Deployment Scripts

The deployment scripts provide convenient commands for managing the application:

```bash
# Deploy in production mode
./scripts/deploy.sh production

# Deploy in development mode
./scripts/deploy.sh development

# Stop all services
./scripts/deploy.sh stop

# View logs
./scripts/deploy.sh logs

# View logs for specific service
./scripts/deploy.sh logs backend

# Check service status
./scripts/deploy.sh status

# Clean up all resources
./scripts/deploy.sh cleanup
```

### Manual Docker Compose Commands

If you prefer using Docker Compose directly:

```bash
# Production deployment
docker-compose up --build -d

# Development deployment
docker-compose -f docker-compose.dev.yml up --build -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f

# Check status
docker-compose ps
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. Port Already in Use
```bash
# Error: Port 80 is already in use
# Solution: Change the port in .env file
FRONTEND_PORT=8080
```

#### 2. Docker Not Running
```bash
# Error: Cannot connect to Docker daemon
# Solution: Start Docker service
sudo systemctl start docker  # Linux
# or start Docker Desktop on Windows/macOS
```

#### 3. Permission Denied (Linux/macOS)
```bash
# Error: Permission denied when running scripts
# Solution: Make scripts executable
chmod +x scripts/deploy.sh
```

#### 4. Database Connection Issues
```bash
# Check MongoDB logs
docker-compose logs mongodb

# Restart MongoDB service
docker-compose restart mongodb
```

#### 5. Build Failures
```bash
# Clean build cache and rebuild
docker-compose down
docker system prune -f
docker-compose up --build --force-recreate
```

### Health Checks

The system includes health checks for all services:

- **MongoDB**: Checks database connectivity
- **Backend**: Checks API health endpoint
- **Frontend**: Checks web server response

View health status:
```bash
docker-compose ps
```

### Logs and Monitoring

Access logs for debugging:

```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f mongodb

# Last 100 lines
docker-compose logs --tail=100 backend
```

## 📊 Performance and Scaling

### Resource Requirements

**Minimum Requirements:**
- CPU: 2 cores
- RAM: 4GB
- Storage: 10GB

**Recommended for Production:**
- CPU: 4+ cores
- RAM: 8GB+
- Storage: 50GB+ (SSD recommended)

### Scaling Options

#### Horizontal Scaling
```yaml
# In docker-compose.yml, add replicas
services:
  backend:
    deploy:
      replicas: 3
```

#### Load Balancing
Consider using nginx or a cloud load balancer for multiple backend instances.

#### Database Scaling
For production, consider:
- MongoDB replica sets
- Sharding for large datasets
- External managed MongoDB service

## 🔒 Security Best Practices

### Production Security Checklist

- [ ] Change all default passwords
- [ ] Use strong JWT secrets (32+ characters)
- [ ] Enable HTTPS with SSL certificates
- [ ] Configure firewall rules
- [ ] Use Docker secrets for sensitive data
- [ ] Regular security updates
- [ ] Monitor logs for suspicious activity
- [ ] Backup database regularly

### SSL/HTTPS Configuration

For production HTTPS, modify the nginx configuration:

```nginx
server {
    listen 443 ssl;
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    # ... rest of configuration
}
```

## 💾 Backup and Recovery

### Database Backup

```bash
# Create backup
docker exec clinicpro-mongodb mongodump --out /backup

# Copy backup from container
docker cp clinicpro-mongodb:/backup ./mongodb-backup

# Restore from backup
docker exec clinicpro-mongodb mongorestore /backup
```

### Automated Backup Script

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
docker exec clinicpro-mongodb mongodump --out /backup/backup_$DATE
docker cp clinicpro-mongodb:/backup/backup_$DATE ./backups/
```

### File Uploads Backup

```bash
# Backup uploaded files
docker cp clinicpro-backend:/app/uploads ./uploads-backup
```

## 🚀 Production Deployment

### Cloud Deployment Options

#### AWS ECS
- Use AWS ECS with Fargate for serverless containers
- Configure Application Load Balancer
- Use RDS for MongoDB or DocumentDB

#### Google Cloud Run
- Deploy containers to Cloud Run
- Use Cloud SQL for database
- Configure Cloud Load Balancing

#### Azure Container Instances
- Deploy to Azure Container Instances
- Use Azure Database for MongoDB
- Configure Azure Load Balancer

### CI/CD Pipeline

Example GitHub Actions workflow:

```yaml
name: Deploy ClinicPro
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to production
        run: |
          docker-compose up --build -d
```

## 📞 Support and Maintenance

### Regular Maintenance Tasks

1. **Update Docker images** monthly
2. **Monitor disk usage** and clean up logs
3. **Review security logs** weekly
4. **Backup database** daily
5. **Update SSL certificates** before expiration

### Getting Help

- Check logs first: `docker-compose logs`
- Review this documentation
- Check Docker and Docker Compose documentation
- Contact support team

## 📝 License and Credits

ClinicPro Clinic Management System
- Frontend: React/TypeScript with Vite
- Backend: Node.js/TypeScript with Express.js
- Database: MongoDB
- Containerization: Docker & Docker Compose

---

**Happy Deploying! 🎉**

For questions or issues, please refer to the troubleshooting section or contact the development team.