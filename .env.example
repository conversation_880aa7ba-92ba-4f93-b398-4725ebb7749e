# ClinicPro Docker Environment Configuration
# Copy this file to .env and update the values as needed

# ==============================================
# APPLICATION SETTINGS
# ==============================================
NODE_ENV=production
COMPOSE_PROJECT_NAME=clinicpro

# ==============================================
# PORT CONFIGURATION
# ==============================================
# Frontend port (where the web application will be accessible)
FRONTEND_PORT=80

# Backend API port (for direct API access if needed)
BACKEND_PORT=3000

# MongoDB port (for external database access if needed)
MONGO_PORT=27017

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
# MongoDB root credentials
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=clinicpro123

# Database name
MONGO_DATABASE=clinic-pro

# ==============================================
# JWT AUTHENTICATION
# ==============================================
# IMPORTANT: Change this to a strong, unique secret in production
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# ==============================================
# AWS S3 CONFIGURATION (Optional)
# ==============================================
# Leave empty if not using AWS S3 for file storage
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_S3_BUCKET=

# ==============================================
# FRONTEND CONFIGURATION
# ==============================================
# API base URL for frontend (usually http://localhost:3000 for local development)
VITE_API_BASE_URL=http://localhost:3000

# ==============================================
# SECURITY NOTES
# ==============================================
# 1. Always change the default passwords in production
# 2. Use strong, unique JWT secrets
# 3. Consider using Docker secrets for sensitive data in production
# 4. Regularly rotate credentials
# 5. Use HTTPS in production environments