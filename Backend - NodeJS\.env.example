FRONTEND_URL=http://localhost:3000
PORT=3000
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/clinic-pro
JWT_SECRET=your-secret-key

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=
AWS_S3_BUCKET_NAME=

# Optional S3 Settings
AWS_S3_ENDPOINT=https://s3.amazonaws.com
S3_FORCE_PATH_STYLE=false
S3_SIGNED_URL_EXPIRES=3600

# File Upload Settings
UPLOAD_PROVIDER=s3
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,video/mp4,video/avi,video/mov,audio/mp3,audio/wav

# S3 Folder Structure (Optional)
S3_AVATARS_FOLDER=avatars
S3_BANNERS_FOLDER=banners
S3_MEDIA_FOLDER=media
S3_THUMBNAILS_FOLDER=thumbnails