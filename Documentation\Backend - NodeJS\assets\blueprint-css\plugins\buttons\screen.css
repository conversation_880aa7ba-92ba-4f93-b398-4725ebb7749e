/* -------------------------------------------------------------- 
  
   buttons.css
   * Gives you some great CSS-only buttons.
   
   Created by <PERSON> [particletree.com]
   * particletree.com/features/rediscovering-the-button-element

   See Readme.txt in this folder for instructions.

-------------------------------------------------------------- */

a.button, button {
  display:block;
  float:left;
  margin: 0.7em 0.5em 0.7em 0;
  padding:5px 10px 5px 7px;   /* Links */
  
  border:1px solid #dedede;
  border-top:1px solid #eee;
  border-left:1px solid #eee;

  background-color:#f5f5f5;
  font-family:"Lucida Grande", Tahoma, Arial, Verdana, sans-serif;
  font-size:100%;
  line-height:130%;
  text-decoration:none;
  font-weight:bold;
  color:#565656;
  cursor:pointer;
}
button {
  width:auto;
  overflow:visible;
  padding:4px 10px 3px 7px;   /* IE6 */
}
button[type] {
  padding:4px 10px 4px 7px;   /* Firefox */
  line-height:17px;           /* Safari */
}
*:first-child+html button[type] {
  padding:4px 10px 3px 7px;   /* IE7 */
}
button img, a.button img{
  margin:0 3px -3px 0 !important;
  padding:0;
  border:none;
  width:16px;
  height:16px;
  float:none;
}


/* Button colors
-------------------------------------------------------------- */

/* Standard */
button:hover, a.button:hover{
  background-color:#dff4ff;
  border:1px solid #c2e1ef;
  color:#336699;
}
a.button:active{
  background-color:#6299c5;
  border:1px solid #6299c5;
  color:#fff;
}

/* Positive */
body .positive {
  color:#529214;
}
a.positive:hover, button.positive:hover {
  background-color:#E6EFC2;
  border:1px solid #C6D880;
  color:#529214;
}
a.positive:active {
  background-color:#529214;
  border:1px solid #529214;
  color:#fff;
}

/* Negative */
body .negative {
  color:#d12f19;
}
a.negative:hover, button.negative:hover {
  background-color:#fbe3e4;
  border:1px solid #fbc2c4;
  color:#d12f19;
}
a.negative:active {
  background-color:#d12f19;
  border:1px solid #d12f19;
  color:#fff;
}
