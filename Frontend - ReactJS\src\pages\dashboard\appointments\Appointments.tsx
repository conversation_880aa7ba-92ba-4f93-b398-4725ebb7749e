import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAppointments, useUpdateAppointment, useDeleteAppointment } from "@/hooks/useApi";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "@/hooks/use-toast";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Search,
  Plus,
  Filter,
  Calendar,
  Clock,
  MoreVertical,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  Stethoscope,
  Eye,
  Edit,
  ChevronLeft,
  ChevronRight,
  CalendarDays,
  Table as TableIcon,
} from "lucide-react";
import { Appointment as ApiAppointment, Patient as ApiPatient, User as ApiUser } from "@/services/api";
import { apiService } from "@/services/api";
import { serviceApi } from "@/services/api/serviceApi";
import type { Service } from "@/types";
import NewAppointmentModal from "@/components/modals/NewAppointmentModal";

const Appointments = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedDate, setSelectedDate] = useState("all");
  
  // View state
  const [currentView, setCurrentView] = useState<"table" | "calendar">("calendar");
  
  // Calendar state
  const [currentCalendarDate, setCurrentCalendarDate] = useState(new Date());
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(25);

  // Modal states
  const [viewDetailsModal, setViewDetailsModal] = useState<{
    open: boolean;
    appointment: any | null;
  }>({ open: false, appointment: null });

  const [editModal, setEditModal] = useState<{
    open: boolean;
    appointment: any | null;
  }>({ open: false, appointment: null });

  const [cancelModal, setCancelModal] = useState<{
    open: boolean;
    appointment: any | null;
  }>({ open: false, appointment: null });

  // Edit form state
  const [editFormData, setEditFormData] = useState({
    patientId: "",
    doctorId: "",
    nurseId: "",
    serviceId: "",
    date: "",
    time: "",
    duration: "",
    type: "",
    notes: "",
  });

  // State for API data in edit modal
  const [editModalData, setEditModalData] = useState({
    patients: [] as ApiPatient[],
    doctors: [] as ApiUser[],
    nurses: [] as ApiUser[],
    services: [] as Service[],
    loading: false,
  });

  // Build API parameters with date filtering
  const getDateRangeParams = () => {
    if (selectedDate === "all") return {};
    
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Start of today
    
    switch (selectedDate) {
      case "today":
        const endOfToday = new Date(today);
        endOfToday.setHours(23, 59, 59, 999); // End of today
        return {
          start_date: today.toISOString(),
          end_date: endOfToday.toISOString(),
        };
      case "tomorrow":
        const tomorrow = new Date(today);
        tomorrow.setDate(today.getDate() + 1);
        const endOfTomorrow = new Date(tomorrow);
        endOfTomorrow.setHours(23, 59, 59, 999);
        return {
          start_date: tomorrow.toISOString(),
          end_date: endOfTomorrow.toISOString(),
        };
      case "this-week":
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay()); // Sunday
        startOfWeek.setHours(0, 0, 0, 0);
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6); // Saturday
        endOfWeek.setHours(23, 59, 59, 999);
        return {
          start_date: startOfWeek.toISOString(),
          end_date: endOfWeek.toISOString(),
        };
      case "next-week":
        const nextWeekStart = new Date(today);
        nextWeekStart.setDate(today.getDate() + (7 - today.getDay())); // Next Sunday
        nextWeekStart.setHours(0, 0, 0, 0);
        const nextWeekEnd = new Date(nextWeekStart);
        nextWeekEnd.setDate(nextWeekStart.getDate() + 6); // Next Saturday
        nextWeekEnd.setHours(23, 59, 59, 999);
        return {
          start_date: nextWeekStart.toISOString(),
          end_date: nextWeekEnd.toISOString(),
        };
      default:
        return {};
    }
  };

  const apiParams = {
    page: currentPage,
    limit: pageSize,
    ...(selectedStatus !== "all" && { status: selectedStatus }),
    ...getDateRangeParams(),
  };

  // Fetch data from APIs
  const { 
    data: appointmentsData, 
    isLoading: appointmentsLoading, 
    error: appointmentsError 
  } = useAppointments(apiParams);

  // Mutations
  const updateAppointmentMutation = useUpdateAppointment();
  const deleteAppointmentMutation = useDeleteAppointment();

  // Process API data - appointments come from data.appointments and have populated patient_id/doctor_id
  const appointments = (appointmentsData as any)?.data?.appointments || [];
  const pagination = (appointmentsData as any)?.data?.pagination || { 
    page: 1, 
    limit: pageSize, 
    total: 0, 
    pages: 0 
  };

  // Convert API appointment with populated patient/doctor data
  const convertAppointment = (apiAppointment: any) => {
    const patient = apiAppointment.patient_id;
    const doctor = apiAppointment.doctor_id;
    const nurse = apiAppointment.nurse_id;
    
    return {
      id: apiAppointment._id,
      // Store both the ID and the original references for flexibility
      patientId: typeof patient === 'string' ? patient : patient?._id,
      doctorId: typeof doctor === 'string' ? doctor : doctor?._id,
      nurseId: typeof nurse === 'string' ? nurse : nurse?._id,
      // Keep original field names for backup access
      patient_id: patient,
      doctor_id: doctor,
      nurse_id: nurse,
      date: new Date(apiAppointment.appointment_date),
      duration: apiAppointment.duration,
      status: apiAppointment.status,
      notes: apiAppointment.notes || "",
      type: apiAppointment.type,
      createdAt: new Date(apiAppointment.created_at),
      updatedAt: new Date(apiAppointment.updated_at),
      // Include populated patient and doctor data
      patient: patient ? {
        id: patient._id,
        name: `${patient.first_name} ${patient.last_name}`,
        phone: patient.phone,
        email: patient.email,
        avatar: ""
      } : null,
      doctor: doctor ? {
        id: doctor._id,
        name: `${doctor.first_name} ${doctor.last_name}`,
        specialty: doctor.role === 'doctor' ? 'General Medicine' : doctor.role
      } : null,
      nurse: nurse ? {
        id: nurse._id,
        name: `${nurse.first_name} ${nurse.last_name}`,
        specialty: nurse.role
      } : null
    };
  };

  const processedAppointments = appointments.map(convertAppointment);

  // For the main table, use the paginated results from API
  // Sort appointments in descending order by date (newest first) - API should handle this but we ensure it
  const sortedAppointments = [...processedAppointments].sort((a, b) => {
    return new Date(b.date).getTime() - new Date(a.date).getTime();
  });

  // Apply search filter only (status and date filters are handled by API params)
  const filteredAppointments = sortedAppointments.filter((appointment) => {
    if (!searchTerm) return true;
    
    const matchesSearch =
      appointment.patient?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.doctor?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.notes.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedStatus, selectedDate, searchTerm]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "scheduled":
      case "confirmed":
        return <Clock className="h-4 w-4 text-blue-600" />;
      case "cancelled":
        return <XCircle className="h-4 w-4 text-red-600" />;
      case "no-show":
        return <AlertCircle className="h-4 w-4 text-orange-600" />;
      case "in-progress":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "scheduled":
      case "confirmed":
        return "bg-blue-100 text-blue-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      case "no-show":
        return "bg-orange-100 text-orange-800";
      case "in-progress":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      weekday: "short",
      month: "short",
      day: "numeric",
    });
  };

  // Calendar helper functions
  const getCalendarDays = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    
    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    // Start from Sunday of the week containing the first day
    const startDate = new Date(firstDay);
    startDate.setDate(firstDay.getDate() - firstDay.getDay());
    
    // End on Saturday of the week containing the last day
    const endDate = new Date(lastDay);
    endDate.setDate(lastDay.getDate() + (6 - lastDay.getDay()));
    
    const days = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      days.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return days;
  };

  const getAppointmentsForDate = (date: Date) => {
    return allFilteredAppointments.filter(appointment => {
      const appointmentDate = new Date(appointment.date);
      return appointmentDate.toDateString() === date.toDateString();
    });
  };

  const isToday = (date: Date) => {
    return date.toDateString() === new Date().toDateString();
  };

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentCalendarDate.getMonth() && 
           date.getFullYear() === currentCalendarDate.getFullYear();
  };

  const navigateCalendar = (direction: 'prev' | 'next') => {
    setCurrentCalendarDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  // Fetch all appointments for stats (without pagination)
  const { data: allAppointmentsData } = useAppointments({ 
    limit: 1000, // Large limit to get all appointments for stats
    ...(selectedStatus !== "all" && { status: selectedStatus })
  });
  
  const allAppointments = (allAppointmentsData as any)?.data?.appointments || [];
  const allProcessedAppointments = allAppointments.map(convertAppointment);
  
  // Sort all appointments in descending order by date (newest first)
  const allSortedAppointments = [...allProcessedAppointments].sort((a, b) => {
    return new Date(b.date).getTime() - new Date(a.date).getTime();
  });

  // Apply the same filters to all appointments for stats
  const allFilteredAppointments = allSortedAppointments.filter((appointment) => {
    const matchesSearch =
      appointment.patient?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.doctor?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.notes.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      selectedStatus === "all" || appointment.status === selectedStatus;

    // Date filtering - use the same logic as API params
    let matchesDate = true;
    if (selectedDate !== "all") {
      const appointmentDate = new Date(appointment.date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      switch (selectedDate) {
        case "today":
          const endOfToday = new Date(today);
          endOfToday.setHours(23, 59, 59, 999);
          matchesDate = appointmentDate >= today && appointmentDate <= endOfToday;
          break;
        case "tomorrow":
          const tomorrow = new Date(today);
          tomorrow.setDate(today.getDate() + 1);
          const endOfTomorrow = new Date(tomorrow);
          endOfTomorrow.setHours(23, 59, 59, 999);
          matchesDate = appointmentDate >= tomorrow && appointmentDate <= endOfTomorrow;
          break;
        case "this-week":
          const startOfWeek = new Date(today);
          startOfWeek.setDate(today.getDate() - today.getDay());
          startOfWeek.setHours(0, 0, 0, 0);
          const endOfWeek = new Date(startOfWeek);
          endOfWeek.setDate(startOfWeek.getDate() + 6);
          endOfWeek.setHours(23, 59, 59, 999);
          matchesDate = appointmentDate >= startOfWeek && appointmentDate <= endOfWeek;
          break;
        case "next-week":
          const nextWeekStart = new Date(today);
          nextWeekStart.setDate(today.getDate() + (7 - today.getDay()));
          nextWeekStart.setHours(0, 0, 0, 0);
          const nextWeekEnd = new Date(nextWeekStart);
          nextWeekEnd.setDate(nextWeekStart.getDate() + 6);
          nextWeekEnd.setHours(23, 59, 59, 999);
          matchesDate = appointmentDate >= nextWeekStart && appointmentDate <= nextWeekEnd;
          break;
        default:
          matchesDate = true;
      }
    }

    return matchesSearch && matchesStatus && matchesDate;
  });

  // Calculate stats based on all filtered appointments
  const todayStats = {
    total: allFilteredAppointments.length,
    today: allFilteredAppointments.filter(apt => 
      apt.date.toDateString() === new Date().toDateString()
    ).length,
    completed: allFilteredAppointments.filter((a) => a.status === "completed").length,
    scheduled: allFilteredAppointments.filter((a) => a.status === "scheduled" || a.status === "confirmed").length,
    cancelled: allFilteredAppointments.filter((a) => a.status === "cancelled" || a.status === "no-show").length,
  };

  // Pagination functions
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.pages) {
      setCurrentPage(newPage);
    }
  };

  const handlePageSizeChange = (newPageSize: string) => {
    setPageSize(parseInt(newPageSize));
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Load data for edit modal
  const loadEditModalData = async () => {
    setEditModalData(prev => ({ ...prev, loading: true }));
    try {
      const [patientsResponse, doctorsResponse, nursesResponse, servicesResponse] = await Promise.all([
        apiService.getPatients({ limit: 100 }),
        apiService.getDoctors({ limit: 100 }),
        apiService.getNurses({ limit: 100 }),
        serviceApi.getServices({ isActive: true, limit: 100 })
      ]);

      setEditModalData({
        patients: patientsResponse.data.patients || [],
        doctors: doctorsResponse.data.items || [],
        nurses: nursesResponse.data.items || [],
        services: servicesResponse.data || [],
        loading: false,
      });
    } catch (error) {
      console.error('Error loading edit modal data:', error);
      setEditModalData(prev => ({ ...prev, loading: false }));
      toast({
        title: "Error loading data",
        description: "Failed to load patients, doctors, nurses, and services.",
        variant: "destructive",
      });
    }
  };

  // Action handlers
  const handleViewDetails = (appointment: any) => {
    setViewDetailsModal({ open: true, appointment });
  };

  const handleEditAppointment = (appointment: any) => {
    // Pre-populate form with current appointment data
    // Get the actual IDs from the appointment data
    const appointmentDate = new Date(appointment.date);
    
    // Extract patient ID - handle both populated and non-populated cases
    const patientId = appointment.patientId || 
                      (appointment.patient && appointment.patient.id) ||
                      (appointment.patient_id && typeof appointment.patient_id === 'string' ? appointment.patient_id : appointment.patient_id?._id) ||
                      "";
    
    // Extract doctor ID - handle both populated and non-populated cases  
    const doctorId = appointment.doctorId ||
                     (appointment.doctor && appointment.doctor.id) ||
                     (appointment.doctor_id && typeof appointment.doctor_id === 'string' ? appointment.doctor_id : appointment.doctor_id?._id) ||
                     "";
    
    // Extract nurse ID if present
    const nurseId = appointment.nurseId ||
                    (appointment.nurse && appointment.nurse.id) ||
                    (appointment.nurse_id && typeof appointment.nurse_id === 'string' ? appointment.nurse_id : appointment.nurse_id?._id) ||
                    "none";
    
    setEditFormData({
      patientId: patientId,
      doctorId: doctorId,
      nurseId: nurseId,
      serviceId: appointment.serviceId || "",
      date: appointmentDate.toISOString().split('T')[0], // YYYY-MM-DD format
      time: appointmentDate.toTimeString().slice(0, 5), // HH:MM format
      duration: appointment.duration ? appointment.duration.toString() : "30",
      type: appointment.type || "consultation",
      notes: appointment.notes || "",
    });
    setEditModal({ open: true, appointment });
    loadEditModalData();
  };

  const handleReschedule = (appointment: any) => {
    // For now, just show edit modal with focus on date/time
    setEditModal({ open: true, appointment });
    toast({
      title: "Reschedule",
      description: "Update the date and time to reschedule this appointment.",
    });
  };

  const handleMarkComplete = async (appointment: any) => {
    try {
      await updateAppointmentMutation.mutateAsync({
        id: appointment.id,
        data: { status: 'completed' }
      });
      toast({
        title: "Success",
        description: "Appointment marked as completed.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update appointment status.",
        variant: "destructive",
      });
    }
  };

  const handleCancelAppointment = (appointment: any) => {
    setCancelModal({ open: true, appointment });
  };

  const confirmCancelAppointment = async () => {
    if (!cancelModal.appointment) return;
    
    try {
      await updateAppointmentMutation.mutateAsync({
        id: cancelModal.appointment.id,
        data: { status: 'cancelled' }
      });
      setCancelModal({ open: false, appointment: null });
      toast({
        title: "Success",
        description: "Appointment has been cancelled.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to cancel appointment.",
        variant: "destructive",
      });
    }
  };

  const handleSaveEdit = async () => {
    if (!editModal.appointment) return;

    // Validate required fields
    if (!editFormData.patientId || editFormData.patientId === "loading" || editFormData.patientId === "no-patients") {
      toast({
        title: "Validation Error",
        description: "Please select a valid patient.",
        variant: "destructive",
      });
      return;
    }

    if (!editFormData.doctorId || editFormData.doctorId === "loading" || editFormData.doctorId === "no-doctors") {
      toast({
        title: "Validation Error", 
        description: "Please select a valid doctor.",
        variant: "destructive",
      });
      return;
    }

    if (!editFormData.date || !editFormData.time) {
      toast({
        title: "Validation Error",
        description: "Please provide both date and time for the appointment.",
        variant: "destructive",
      });
      return;
    }

    if (!editFormData.type || editFormData.type === "") {
      toast({
        title: "Validation Error",
        description: "Please select an appointment type.",
        variant: "destructive",
      });
      return;
    }

    const duration = parseInt(editFormData.duration);
    if (isNaN(duration) || duration < 15 || duration > 240) {
      toast({
        title: "Validation Error",
        description: "Duration must be between 15 and 240 minutes.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Combine date and time into a single datetime string
      const appointmentDateTime = new Date(`${editFormData.date}T${editFormData.time}`);
      
      // Validate the date is not invalid
      if (isNaN(appointmentDateTime.getTime())) {
        toast({
          title: "Validation Error",
          description: "Please provide a valid date and time.",
          variant: "destructive",
        });
        return;
      }
      
      const updateData: {
        patient_id: string;
        doctor_id: string;
        appointment_date: string;
        duration: number;
        type: string;
        notes: string;
        nurse_id?: string;
      } = {
        patient_id: editFormData.patientId,
        doctor_id: editFormData.doctorId,
        appointment_date: appointmentDateTime.toISOString(),
        duration: duration,
        type: editFormData.type,
        notes: editFormData.notes || "",
      };

      // Only add nurse_id if a nurse is selected
      if (editFormData.nurseId && editFormData.nurseId !== 'none') {
        updateData.nurse_id = editFormData.nurseId;
      }

      console.log('Updating appointment with data:', updateData);

      await updateAppointmentMutation.mutateAsync({
        id: editModal.appointment.id,
        data: updateData
      });

      setEditModal({ open: false, appointment: null });
      toast({
        title: "Success",
        description: "Appointment has been updated successfully.",
      });
    } catch (error) {
      console.error('Update appointment error:', error);
      toast({
        title: "Error",
        description: "Failed to update appointment. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Loading state
  if (appointmentsLoading) {
    return (
      <div className="space-y-6 w-full max-w-full overflow-hidden">
        <div className="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex-1">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96 mt-2" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 w-full">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-8 w-12" />
                  </div>
                  <Skeleton className="h-8 w-8 rounded-full" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <div className="flex gap-4">
                <Skeleton className="h-10 w-40" />
                <Skeleton className="h-10 w-40" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-96" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-48" />
                    <Skeleton className="h-3 w-32" />
                  </div>
                  <Skeleton className="h-6 w-20" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (appointmentsError) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load appointments. Please check your connection and try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-3 xs:space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-3 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex-1 min-w-0">
          <h1 className="text-xl xs:text-2xl sm:text-3xl font-bold text-gray-900">
            Appointments
          </h1>
          <p className="text-xs xs:text-sm sm:text-base text-gray-600 mt-1">
            Manage patient appointments and schedules
          </p>
        </div>
        <div className="flex-shrink-0 w-full sm:w-auto">
          <NewAppointmentModal />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-4 gap-2 xs:gap-3 sm:gap-4 lg:gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card>
            <CardContent className="p-3 xs:p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-gray-600">
                    Today's Appointments
                  </p>
                  <p className="text-lg xs:text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mt-1">
                    {appointmentsLoading ? "..." : todayStats.today}
                  </p>
                </div>
                <Calendar className="h-6 w-6 xs:h-8 xs:w-8 text-blue-600 flex-shrink-0 ml-2" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card>
            <CardContent className="p-3 xs:p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-gray-600">
                    Completed
                  </p>
                  <p className="text-lg xs:text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mt-1">
                    {appointmentsLoading ? "..." : todayStats.completed}
                  </p>
                </div>
                <CheckCircle className="h-6 w-6 xs:h-8 xs:w-8 text-green-600 flex-shrink-0 ml-2" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card>
            <CardContent className="p-3 xs:p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-gray-600">
                    Pending
                  </p>
                  <p className="text-lg xs:text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mt-1">
                    {appointmentsLoading ? "..." : todayStats.scheduled}
                  </p>
                </div>
                <Clock className="h-6 w-6 xs:h-8 xs:w-8 text-orange-600 flex-shrink-0 ml-2" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Card>
            <CardContent className="p-3 xs:p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-gray-600">
                    Cancelled
                  </p>
                  <p className="text-lg xs:text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mt-1">
                    {appointmentsLoading ? "..." : todayStats.cancelled}
                  </p>
                </div>
                <XCircle className="h-6 w-6 xs:h-8 xs:w-8 text-red-600 flex-shrink-0 ml-2" />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-3 xs:p-4 sm:p-6">
          <div className="flex flex-col gap-3 sm:gap-4">
            {/* Search Bar */}
            <div className="relative flex-1 min-w-0">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search appointments by patient or doctor..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-full h-10"
              />
            </div>

            {/* Filter Controls */}
            <div className="flex flex-col xs:flex-row gap-2 xs:gap-3 sm:gap-4">
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-full xs:w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="no-show">No Show</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedDate} onValueChange={setSelectedDate}>
                <SelectTrigger className="w-full xs:w-48">
                  <SelectValue placeholder="Date Range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="tomorrow">Tomorrow</SelectItem>
                  <SelectItem value="this-week">This Week</SelectItem>
                  <SelectItem value="next-week">Next Week</SelectItem>
                  <SelectItem value="all">All Dates</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* View Selector and Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Card>
          <CardHeader className="pb-3">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
              <div>
                <CardTitle className="text-base xs:text-lg sm:text-xl">Appointment Schedule</CardTitle>
                <CardDescription className="text-xs xs:text-sm">
                  Manage and track all patient appointments
                </CardDescription>
              </div>
              <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                <Button
                  variant={currentView === "table" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setCurrentView("table")}
                  className="h-8 px-3"
                >
                  <TableIcon className="h-4 w-4 mr-1" />
                  Table
                </Button>
                <Button
                  variant={currentView === "calendar" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setCurrentView("calendar")}
                  className="h-8 px-3"
                >
                  <CalendarDays className="h-4 w-4 mr-1" />
                  Calendar
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="px-3 xs:px-4 sm:px-6">
            {currentView === "table" ? (
              <>
                {/* Desktop Table View */}
                <div className="hidden lg:block">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Patient</TableHead>
                    <TableHead>Doctor</TableHead>
                    <TableHead>Date & Time</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {appointmentsLoading ? (
                    // Loading skeletons
                    Array.from({ length: 5 }).map((_, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Skeleton className="h-10 w-10 rounded-full" />
                            <div className="space-y-2">
                              <Skeleton className="h-4 w-32" />
                              <Skeleton className="h-3 w-16" />
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-2">
                            <Skeleton className="h-4 w-28" />
                            <Skeleton className="h-3 w-20" />
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-2">
                            <Skeleton className="h-4 w-24" />
                            <Skeleton className="h-3 w-16" />
                          </div>
                        </TableCell>
                        <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                        <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                        <TableCell><Skeleton className="h-8 w-24" /></TableCell>
                      </TableRow>
                    ))
                  ) : appointmentsError ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <Alert variant="destructive" className="mx-auto max-w-md">
                          <AlertDescription>
                            Failed to load appointments. Please try again.
                          </AlertDescription>
                        </Alert>
                      </TableCell>
                    </TableRow>
                  ) : filteredAppointments.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="text-gray-500">
                          <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No appointments found</p>
                          {searchTerm && (
                            <p className="text-xs mt-1">
                              Try adjusting your search criteria
                            </p>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredAppointments.map((appointment) => (
                      <TableRow key={appointment.id} className="hover:bg-gray-50">
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-10 w-10">
                              <AvatarFallback>
                                {appointment.patient?.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium text-gray-900">
                                {appointment.patient?.name || "Unknown Patient"}
                              </p>
                              <p className="text-sm text-gray-500">
                                {appointment.patient?.phone}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium text-gray-900">
                              {appointment.doctor?.name || "Unknown Doctor"}
                            </p>
                            <p className="text-sm text-gray-500">
                              {appointment.doctor?.specialty}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium text-gray-900">
                              {formatDate(appointment.date)}
                            </p>
                            <p className="text-sm text-gray-500">
                              {formatTime(appointment.date)} • {appointment.duration} min
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="text-xs">
                            {appointment.type}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary" className={`text-xs ${getStatusColor(appointment.status)}`}>
                            {getStatusIcon(appointment.status)}
                            <span className="ml-1 capitalize">{appointment.status}</span>
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="outline" size="sm" className="h-8">
                                <MoreVertical className="h-4 w-4 mr-1" />
                                Actions
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleViewDetails(appointment)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEditAppointment(appointment)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              {appointment.status !== "completed" && (
                                <DropdownMenuItem onClick={() => handleMarkComplete(appointment)}>
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  Mark Complete
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem 
                                onClick={() => handleCancelAppointment(appointment)}
                                className="text-red-600"
                              >
                                <XCircle className="mr-2 h-4 w-4" />
                                Cancel
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

                          {/* Mobile Card View */}
              <div className="lg:hidden space-y-3">
                {appointmentsLoading ? (
                  // Loading skeletons for mobile
                  Array.from({ length: 5 }).map((_, index) => (
                    <Card key={index} className="p-4">
                      <div className="flex items-center space-x-3">
                        <Skeleton className="h-12 w-12 rounded-full" />
                        <div className="flex-1 space-y-2">
                          <Skeleton className="h-4 w-32" />
                          <Skeleton className="h-3 w-24" />
                          <Skeleton className="h-3 w-40" />
                        </div>
                        <Skeleton className="h-8 w-8" />
                      </div>
                    </Card>
                  ))
                ) : appointmentsError ? (
                  <Alert variant="destructive">
                    <AlertDescription>
                      Failed to load appointments. Please try again.
                    </AlertDescription>
                  </Alert>
                ) : filteredAppointments.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p className="text-sm font-medium">No appointments found</p>
                    {searchTerm && (
                      <p className="text-xs mt-1">
                        Try adjusting your search criteria
                      </p>
                    )}
                  </div>
                ) : (
                  filteredAppointments.map((appointment) => (
                    <Card key={appointment.id} className="p-4 hover:shadow-md transition-shadow border border-gray-200 bg-white">
                      {/* Header with patient and status */}
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3 flex-1 min-w-0">
                          <Avatar className="h-10 w-10 flex-shrink-0">
                            <AvatarFallback className="text-sm">
                              {appointment.patient?.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <p className="font-semibold text-sm text-gray-900 truncate">
                              {appointment.patient?.name || "Unknown Patient"}
                            </p>
                            <p className="text-xs text-gray-500">
                              {appointment.patient?.phone}
                            </p>
                          </div>
                        </div>
                        <Badge 
                          variant="secondary" 
                          className={`text-xs flex items-center space-x-1 ${getStatusColor(appointment.status)}`}
                        >
                          {getStatusIcon(appointment.status)}
                          <span className="capitalize">{appointment.status}</span>
                        </Badge>
                      </div>

                      {/* Appointment details */}
                      <div className="space-y-2 p-3 bg-gray-50 rounded-lg mb-3">
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center space-x-2">
                            <Stethoscope className="h-4 w-4 text-blue-600" />
                            <span className="font-medium">Doctor</span>
                          </div>
                          <span className="text-gray-900">
                            {appointment.doctor?.name || "Unknown Doctor"}
                          </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-green-600" />
                            <span className="font-medium">Date</span>
                          </div>
                          <span className="text-gray-900">
                            {formatDate(appointment.date)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-orange-600" />
                            <span className="font-medium">Time</span>
                          </div>
                          <span className="text-gray-900">
                            {formatTime(appointment.date)} ({appointment.duration} min)
                          </span>
                        </div>
                      </div>

                      {/* Type and actions */}
                      <div className="flex items-center justify-between">
                        <Badge variant="outline" className="text-xs">
                          {appointment.type}
                        </Badge>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="h-8">
                              <MoreVertical className="h-4 w-4 mr-1" />
                              Actions
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-48">
                            <DropdownMenuItem onClick={() => handleViewDetails(appointment)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditAppointment(appointment)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            {appointment.status !== "completed" && (
                              <DropdownMenuItem onClick={() => handleMarkComplete(appointment)}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Mark Complete
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem 
                              onClick={() => handleCancelAppointment(appointment)}
                              className="text-red-600"
                            >
                              <XCircle className="mr-2 h-4 w-4" />
                              Cancel
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </Card>
                  ))
                )}
              </div>

            {/* Functional Pagination */}
            <div className="flex flex-col xs:flex-row items-center justify-between gap-3 xs:gap-4 mt-4 pt-4 border-t">
              <div className="flex flex-col xs:flex-row items-center gap-2 xs:gap-4">
                <div className="text-xs xs:text-sm text-gray-500">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} appointments
                </div>
                <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>
                  <SelectTrigger className="w-20 h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center space-x-1">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="text-xs h-8 px-2"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage <= 1}
                >
                  <ChevronLeft className="h-3 w-3 mr-1" />
                  Previous
                </Button>
                
                {/* Page numbers */}
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                    let pageNum: number;
                    if (pagination.pages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= pagination.pages - 2) {
                      pageNum = pagination.pages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="sm"
                        className="text-xs h-8 w-8 p-0"
                        onClick={() => handlePageChange(pageNum)}
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="text-xs h-8 px-2"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= pagination.pages}
                >
                  Next
                  <ChevronRight className="h-3 w-3 ml-1" />
                </Button>
              </div>
            </div>
                </>
              ) : (
                /* Calendar View */
                <div className="space-y-4">
                  {/* Calendar Header */}
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">
                      {currentCalendarDate.toLocaleDateString("en-US", {
                        month: "long",
                        year: "numeric",
                      })}
                    </h3>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigateCalendar('prev')}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentCalendarDate(new Date())}
                      >
                        Today
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigateCalendar('next')}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Desktop Calendar Grid */}
                  <div className="hidden md:grid grid-cols-7 gap-1">
                    {/* Day headers */}
                    {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
                      <div
                        key={day}
                        className="p-2 text-center text-sm font-medium text-gray-500 border-b"
                      >
                        {day}
                      </div>
                    ))}

                    {/* Calendar days */}
                    {getCalendarDays(currentCalendarDate).map((date, index) => {
                      const dayAppointments = getAppointmentsForDate(date);
                      const isCurrentMonthDay = isCurrentMonth(date);
                      const isTodayDate = isToday(date);

                      return (
                        <div
                          key={index}
                          className={`
                            min-h-[120px] p-2 border border-gray-200 
                            ${!isCurrentMonthDay ? "bg-gray-50 text-gray-400" : "bg-white"}
                            ${isTodayDate ? "bg-blue-50 border-blue-300" : ""}
                            hover:bg-gray-50 transition-colors
                          `}
                        >
                          <div className="flex items-center justify-between mb-1">
                            <span
                              className={`
                                text-sm font-medium
                                ${isTodayDate ? "text-blue-600 font-bold" : ""}
                              `}
                            >
                              {date.getDate()}
                            </span>
                            {dayAppointments.length > 0 && (
                              <Badge
                                variant="secondary"
                                className="text-xs h-5 px-1"
                              >
                                {dayAppointments.length}
                              </Badge>
                            )}
                          </div>

                          {/* Appointments for the day */}
                          <div className="space-y-1">
                            {dayAppointments.slice(0, 3).map((appointment) => (
                              <div
                                key={appointment.id}
                                className={`
                                  text-xs p-1 rounded cursor-pointer
                                  ${getStatusColor(appointment.status)}
                                  hover:opacity-80 transition-opacity
                                `}
                                onClick={() => handleViewDetails(appointment)}
                                title={`${appointment.patient?.name} - ${formatTime(appointment.date)}`}
                              >
                                <div className="truncate font-medium">
                                  {appointment.patient?.name}
                                </div>
                                <div className="truncate text-xs opacity-80">
                                  {formatTime(appointment.date)}
                                </div>
                              </div>
                            ))}
                            {dayAppointments.length > 3 && (
                              <div className="text-xs text-gray-500 text-center">
                                +{dayAppointments.length - 3} more
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Mobile Calendar View */}
                  <div className="md:hidden space-y-2">
                    {getCalendarDays(currentCalendarDate)
                      .filter((date) => isCurrentMonth(date))
                      .map((date) => {
                        const dayAppointments = getAppointmentsForDate(date);
                        const isTodayDate = isToday(date);

                        return (
                          <Card
                            key={date.toDateString()}
                            className={`p-3 ${isTodayDate ? "border-blue-300 bg-blue-50" : ""}`}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <h4 className={`font-medium ${isTodayDate ? "text-blue-600" : ""}`}>
                                {date.toLocaleDateString("en-US", {
                                  weekday: "short",
                                  month: "short",
                                  day: "numeric",
                                })}
                                {isTodayDate && (
                                  <Badge variant="secondary" className="ml-2 text-xs">
                                    Today
                                  </Badge>
                                )}
                              </h4>
                              {dayAppointments.length > 0 && (
                                <Badge variant="outline" className="text-xs">
                                  {dayAppointments.length} appointment{dayAppointments.length !== 1 ? 's' : ''}
                                </Badge>
                              )}
                            </div>

                            {dayAppointments.length === 0 ? (
                              <p className="text-sm text-gray-500">No appointments</p>
                            ) : (
                              <div className="space-y-2">
                                {dayAppointments.map((appointment) => (
                                  <div
                                    key={appointment.id}
                                    className={`
                                      p-2 rounded-lg cursor-pointer border
                                      ${getStatusColor(appointment.status)}
                                      hover:opacity-80 transition-opacity
                                    `}
                                    onClick={() => handleViewDetails(appointment)}
                                  >
                                    <div className="flex items-center justify-between">
                                      <div className="flex-1 min-w-0">
                                        <p className="font-medium text-sm truncate">
                                          {appointment.patient?.name}
                                        </p>
                                        <p className="text-xs opacity-80">
                                          {formatTime(appointment.date)} • {appointment.type}
                                        </p>
                                      </div>
                                      <div className="ml-2 flex items-center">
                                        {getStatusIcon(appointment.status)}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </Card>
                        );
                      })}
                  </div>

                  {/* Calendar Legend */}
                  <div className="flex flex-wrap gap-4 text-xs text-gray-600 pt-4 border-t">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-100 border border-green-200 rounded"></div>
                      <span>Completed</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-blue-100 border border-blue-200 rounded"></div>
                      <span>Scheduled</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-100 border border-red-200 rounded"></div>
                      <span>Cancelled</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-orange-100 border border-orange-200 rounded"></div>
                      <span>No Show</span>
                    </div>
                  </div>
                </div>
              )}
          </CardContent>
        </Card>
      </motion.div>

      {/* View Details Modal */}
      <Dialog open={viewDetailsModal.open} onOpenChange={(open) => setViewDetailsModal({ open, appointment: null })}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Appointment Details</DialogTitle>
            <DialogDescription>
              View complete appointment information
            </DialogDescription>
          </DialogHeader>
          {viewDetailsModal.appointment && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-sm text-gray-500 uppercase tracking-wide">Patient</h4>
                  <p className="text-lg font-medium">{viewDetailsModal.appointment.patient?.name}</p>
                  <p className="text-sm text-gray-600">{viewDetailsModal.appointment.patient?.phone}</p>
                  <p className="text-sm text-gray-600">{viewDetailsModal.appointment.patient?.email}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-sm text-gray-500 uppercase tracking-wide">Doctor</h4>
                  <p className="text-lg font-medium">{viewDetailsModal.appointment.doctor?.name}</p>
                  <p className="text-sm text-gray-600">{viewDetailsModal.appointment.doctor?.specialty}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-sm text-gray-500 uppercase tracking-wide">Date & Time</h4>
                  <p className="text-lg font-medium">{formatDate(viewDetailsModal.appointment.date)} at {formatTime(viewDetailsModal.appointment.date)}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-sm text-gray-500 uppercase tracking-wide">Duration</h4>
                  <p className="text-lg font-medium">{viewDetailsModal.appointment.duration} minutes</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-sm text-gray-500 uppercase tracking-wide">Type</h4>
                  <p className="text-lg font-medium capitalize">{viewDetailsModal.appointment.type}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-sm text-gray-500 uppercase tracking-wide">Status</h4>
                  <Badge className={`${getStatusColor(viewDetailsModal.appointment.status)}`}>
                    {viewDetailsModal.appointment.status}
                  </Badge>
                </div>
              </div>

              {viewDetailsModal.appointment.notes && (
                <div>
                  <h4 className="font-semibold text-sm text-gray-500 uppercase tracking-wide">Notes</h4>
                  <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">{viewDetailsModal.appointment.notes}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Appointment Modal */}
      <Dialog open={editModal.open} onOpenChange={(open) => setEditModal({ open, appointment: null })}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Appointment</DialogTitle>
            <DialogDescription>
              Update appointment details for {editModal.appointment?.patient?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6">
            {/* Patient, Doctor and Nurse Selection */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Patient, Doctor & Nurse</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-patient">Select Patient *</Label>
                  <Select
                    value={editFormData.patientId}
                    onValueChange={(value) => setEditFormData(prev => ({ ...prev, patientId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a patient" />
                    </SelectTrigger>
                    <SelectContent>
                      {editModalData.loading ? (
                        <SelectItem value="loading" disabled>
                          Loading patients...
                        </SelectItem>
                      ) : (
                        editModalData.patients.map((patient) => (
                          <SelectItem key={patient._id} value={patient._id}>
                            <div className="flex flex-col">
                              <span className="font-medium">
                                {patient.first_name} {patient.last_name}
                              </span>
                              <span className="text-xs text-gray-500">
                                {patient.phone}
                              </span>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-doctor">Select Doctor *</Label>
                  <Select
                    value={editFormData.doctorId}
                    onValueChange={(value) => setEditFormData(prev => ({ ...prev, doctorId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a doctor" />
                    </SelectTrigger>
                    <SelectContent>
                      {editModalData.loading ? (
                        <SelectItem value="loading" disabled>
                          Loading doctors...
                        </SelectItem>
                      ) : editModalData.doctors.length === 0 ? (
                        <SelectItem value="no-doctors" disabled>
                          No doctors available.
                        </SelectItem>
                      ) : (
                        editModalData.doctors.map((doctor) => (
                          <SelectItem key={doctor._id} value={doctor._id}>
                            <div className="flex flex-col">
                              <span className="font-medium">
                                {doctor.first_name} {doctor.last_name}
                              </span>
                              <span className="text-xs text-gray-500">
                                {doctor.role} • {doctor.phone}
                              </span>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-nurse">Select Nurse (Optional)</Label>
                  <Select
                    value={editFormData.nurseId}
                    onValueChange={(value) => setEditFormData(prev => ({ ...prev, nurseId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a nurse (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No nurse assigned</SelectItem>
                      {editModalData.loading ? (
                        <SelectItem value="loading" disabled>
                          Loading nurses...
                        </SelectItem>
                      ) : editModalData.nurses.length === 0 ? (
                        <SelectItem value="no-nurses" disabled>
                          No nurses available.
                        </SelectItem>
                      ) : (
                        editModalData.nurses.map((nurse) => (
                          <SelectItem key={nurse._id} value={nurse._id}>
                            <div className="flex flex-col">
                              <span className="font-medium">
                                {nurse.first_name} {nurse.last_name}
                              </span>
                              <span className="text-xs text-gray-500">
                                {nurse.role} • {nurse.phone}
                              </span>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Service Selection */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Service & Schedule</h3>
              <div className="space-y-2">
                <Label htmlFor="edit-service">Service Type</Label>
                <Select
                  value={editFormData.serviceId}
                  onValueChange={(value) => {
                    setEditFormData(prev => ({ ...prev, serviceId: value }));
                    // Auto-set duration when service is selected
                    const selectedService = editModalData.services.find((s) => s.id === value);
                    if (selectedService) {
                      setEditFormData(prev => ({
                        ...prev,
                        duration: selectedService.duration.toString(),
                      }));
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select service type" />
                  </SelectTrigger>
                  <SelectContent>
                    {editModalData.loading ? (
                      <SelectItem value="loading" disabled>
                        Loading services...
                      </SelectItem>
                    ) : editModalData.services.length === 0 ? (
                      <SelectItem value="no-services" disabled>
                        No services available.
                      </SelectItem>
                    ) : (
                      editModalData.services.map((service) => (
                        <SelectItem key={service.id} value={service.id}>
                          <div className="flex justify-between w-full">
                            <span className="font-medium">{service.name}</span>
                            <span className="text-sm text-gray-500 ml-4">
                              {service.duration}min - ${service.price}
                            </span>
                          </div>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-date">Date</Label>
                  <Input
                    id="edit-date"
                    type="date"
                    value={editFormData.date}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, date: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-time">Time</Label>
                  <Input
                    id="edit-time"
                    type="time"
                    value={editFormData.time}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, time: e.target.value }))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-duration">Duration (minutes)</Label>
                  <Input
                    id="edit-duration"
                    type="number"
                    min="15"
                    max="240"
                    step="15"
                    value={editFormData.duration}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, duration: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-type">Type</Label>
                  <Select 
                    value={editFormData.type} 
                    onValueChange={(value) => setEditFormData(prev => ({ ...prev, type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="consultation">Consultation</SelectItem>
                      <SelectItem value="follow-up">Follow-up</SelectItem>
                      <SelectItem value="check-up">Check-up</SelectItem>
                      <SelectItem value="vaccination">Vaccination</SelectItem>
                      <SelectItem value="procedure">Procedure</SelectItem>
                      <SelectItem value="emergency">Emergency</SelectItem>
                      <SelectItem value="screening">Screening</SelectItem>
                      <SelectItem value="therapy">Therapy</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Additional Information</h3>
              <div className="space-y-2">
                <Label htmlFor="edit-notes">Notes</Label>
                <Textarea
                  id="edit-notes"
                  placeholder="Add any additional notes..."
                  value={editFormData.notes}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, notes: e.target.value }))}
                  rows={3}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setEditModal({ open: false, appointment: null })}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSaveEdit}
              disabled={updateAppointmentMutation.isPending}
            >
              {updateAppointmentMutation.isPending ? "Saving..." : "Save Changes"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Cancel Confirmation Modal */}
      <AlertDialog open={cancelModal.open} onOpenChange={(open) => setCancelModal({ open, appointment: null })}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cancel Appointment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to cancel this appointment with {cancelModal.appointment?.patient?.name}? 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Keep Appointment</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmCancelAppointment}
              className="bg-red-600 hover:bg-red-700"
            >
              Cancel Appointment
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default Appointments;
