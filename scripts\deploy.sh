#!/bin/bash

# ClinicPro Docker Deployment Script
# This script provides easy deployment commands for the ClinicPro application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "docker-compose is not installed. Please install docker-compose and try again."
        exit 1
    fi
}

# Function to create .env file if it doesn't exist
setup_env() {
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from .env.example..."
        cp .env.example .env
        print_success ".env file created. Please review and update the configuration."
        print_warning "IMPORTANT: Change the default passwords and JWT secret before deploying to production!"
    fi
}

# Function to deploy production environment
deploy_production() {
    print_status "Deploying ClinicPro in production mode..."

    check_docker
    check_docker_compose
    setup_env

    print_status "Building and starting containers..."
    docker-compose down --remove-orphans
    docker-compose up --build -d

    print_status "Waiting for services to be healthy..."
    sleep 30

    # Check if services are running
    if docker-compose ps | grep -q "Up"; then
        print_success "ClinicPro deployed successfully!"
        print_status "Frontend: http://localhost:${FRONTEND_PORT:-80}"
        print_status "Backend API: http://localhost:${BACKEND_PORT:-3000}"
        print_status "MongoDB: localhost:${MONGO_PORT:-27017}"
    else
        print_error "Deployment failed. Check logs with: docker-compose logs"
        exit 1
    fi
}

# Function to deploy development environment
deploy_development() {
    print_status "Deploying ClinicPro in development mode..."

    check_docker
    check_docker_compose
    setup_env

    print_status "Building and starting development containers..."
    docker-compose -f docker-compose.dev.yml down --remove-orphans
    docker-compose -f docker-compose.dev.yml up --build -d

    print_status "Waiting for services to be ready..."
    sleep 30

    print_success "ClinicPro development environment deployed!"
    print_status "Frontend: http://localhost:${FRONTEND_PORT:-5173}"
    print_status "Backend API: http://localhost:${BACKEND_PORT:-3000}"
    print_status "MongoDB: localhost:${MONGO_PORT:-27017}"
}

# Function to stop all services
stop_services() {
    print_status "Stopping ClinicPro services..."
    docker-compose down
    docker-compose -f docker-compose.dev.yml down
    print_success "All services stopped."
}

# Function to show logs
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$service"
    fi
}

# Function to show status
show_status() {
    print_status "ClinicPro Service Status:"
    docker-compose ps
}

# Function to clean up
cleanup() {
    print_status "Cleaning up Docker resources..."
    docker-compose down --volumes --remove-orphans
    docker-compose -f docker-compose.dev.yml down --volumes --remove-orphans
    docker system prune -f
    print_success "Cleanup completed."
}

# Main script logic
case "$1" in
    "production"|"prod")
        deploy_production
        ;;
    "development"|"dev")
        deploy_development
        ;;
    "stop")
        stop_services
        ;;
    "logs")
        show_logs "$2"
        ;;
    "status")
        show_status
        ;;
    "cleanup")
        cleanup
        ;;
    *)
        echo "ClinicPro Docker Deployment Script"
        echo ""
        echo "Usage: $0 {production|development|stop|logs|status|cleanup}"
        echo ""
        echo "Commands:"
        echo "  production   - Deploy in production mode"
        echo "  development  - Deploy in development mode"
        echo "  stop         - Stop all services"
        echo "  logs [service] - Show logs (optionally for specific service)"
        echo "  status       - Show service status"
        echo "  cleanup      - Clean up all Docker resources"
        echo ""
        echo "Examples:"
        echo "  $0 production"
        echo "  $0 development"
        echo "  $0 logs backend"
        echo "  $0 stop"
        exit 1
        ;;
esac