/* -------------------------------------------------------------- 

   forms.css
   * Sets up some default styling for forms
   * Gives you classes to enhance your forms
   
   Usage:
   * For text fields, use class .title or .text
   * For inline forms, use .inline (even when using columns)
   
-------------------------------------------------------------- */

label       { font-weight: bold; }
fieldset    { padding:1.4em; margin: 0 0 1.5em 0; border: 1px solid #ccc; }
legend      { font-weight: bold; font-size:1.2em; }


/* Form fields
-------------------------------------------------------------- */

input[type=text], input[type=password],
input.text, input.title, 
textarea, select { 
  background-color:#fff; 
  border:1px solid #bbb; 
}
input[type=text]:focus, input[type=password]:focus, 
input.text:focus, input.title:focus, 
textarea:focus, select:focus { 
  border-color:#666; 
}

input[type=text], input[type=password],
input.text, input.title,
textarea, select {
  margin:0.5em 0;
}

input.text, 
input.title   { width: 300px; padding:5px; }
input.title   { font-size:1.5em; }
textarea      { width: 390px; height: 250px; padding:5px; }

input[type=checkbox], input[type=radio], 
input.checkbox, input.radio { 
  position:relative; top:.25em; 
}

form.inline { line-height:3; }
form.inline p { margin-bottom:0; }


/* Success, notice and error boxes
-------------------------------------------------------------- */

.error,
.notice, 
.success    { padding: .8em; margin-bottom: 1em; border: 2px solid #ddd; }

.error      { background: #FBE3E4; color: #8a1f11; border-color: #FBC2C4; }
.notice     { background: #FFF6BF; color: #514721; border-color: #FFD324; }
.success    { background: #E6EFC2; color: #264409; border-color: #C6D880; }
.error a    { color: #8a1f11; }
.notice a   { color: #514721; }
.success a  { color: #264409; }
