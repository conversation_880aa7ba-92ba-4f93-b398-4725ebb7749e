import Department, { IDepartment } from '../models/Department';

const departmentData: Partial<IDepartment>[] = [
  {
    code: 'CARD',
    name: 'Cardiology',
    description: 'Diagnosis and treatment of heart and cardiovascular diseases',
    head: 'Dr. <PERSON>',
    location: 'Building A, Floor 2',
    phone: '******-0101',
    email: '<EMAIL>',
    staffCount: 15,
    budget: 500000,
    status: 'active'
  },
  {
    code: 'NEURO',
    name: 'Neurology',
    description: 'Disorders of the nervous system including brain and spinal cord',
    head: 'Dr. <PERSON>',
    location: 'Building B, Floor 3',
    phone: '******-0102',
    email: '<EMAIL>',
    staffCount: 12,
    budget: 450000,
    status: 'active'
  },
  {
    code: 'PEDS',
    name: 'Pediatrics',
    description: 'Medical care for infants, children, and adolescents',
    head: 'Dr. <PERSON>',
    location: 'Building C, Floor 1',
    phone: '******-0103',
    email: '<EMAIL>',
    staffCount: 18,
    budget: 400000,
    status: 'active'
  },
  {
    code: '<PERSON><PERSON><PERSON>',
    name: 'Orthopedics',
    description: 'Treatment of musculoskeletal system disorders',
    head: 'Dr. <PERSON>',
    location: 'Building A, Floor 1',
    phone: '******-0104',
    email: '<EMAIL>',
    staffCount: 10,
    budget: 380000,
    status: 'active'
  },
  {
    code: 'EMER',
    name: 'Emergency Medicine',
    description: '24/7 emergency medical care and trauma treatment',
    head: 'Dr. Lisa Thompson',
    location: 'Building D, Ground Floor',
    phone: '******-0105',
    email: '<EMAIL>',
    staffCount: 25,
    budget: 600000,
    status: 'active'
  },
  {
    code: 'LAB',
    name: 'Laboratory',
    description: 'Clinical laboratory testing and pathology services',
    head: 'Dr. Robert Kim',
    location: 'Building B, Basement',
    phone: '******-0106',
    email: '<EMAIL>',
    staffCount: 8,
    budget: 300000,
    status: 'active'
  },
  {
    code: 'RAD',
    name: 'Radiology',
    description: 'Medical imaging and diagnostic radiology services',
    head: 'Dr. Amanda Davis',
    location: 'Building B, Floor 1',
    phone: '******-0107',
    email: '<EMAIL>',
    staffCount: 6,
    budget: 250000,
    status: 'active'
  },
  {
    code: 'ADMIN',
    name: 'Administration',
    description: 'Administrative and management services',
    head: 'Mrs. Jennifer Brown',
    location: 'Building A, Floor 3',
    phone: '******-0108',
    email: '<EMAIL>',
    staffCount: 12,
    budget: 200000,
    status: 'active'
  }
];

export async function seedDepartments(): Promise<void> {
  try {
    await Department.deleteMany({});
    await Department.insertMany(departmentData);
  } catch (error) {
    throw error;
  }
} 