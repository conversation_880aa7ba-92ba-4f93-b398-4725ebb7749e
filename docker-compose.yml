version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: clinicpro-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-clinicpro123}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE:-clinic-pro}
    ports:
      - "${MONGO_PORT:-27017}:27017"
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - clinicpro-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend API Service
  backend:
    build:
      context: ./Backend - NodeJS
      dockerfile: Dockerfile
    container_name: clinicpro-backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 3000
      MONGODB_URI: mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-clinicpro123}@mongodb:27017/${MONGO_DATABASE:-clinic-pro}?authSource=admin
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-this-in-production}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-7d}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID:-}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY:-}
      AWS_REGION: ${AWS_REGION:-us-east-1}
      AWS_S3_BUCKET: ${AWS_S3_BUCKET:-}
    ports:
      - "${BACKEND_PORT:-3000}:3000"
    volumes:
      - backend_uploads:/app/uploads
    networks:
      - clinicpro-network
    depends_on:
      mongodb:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend Web Application
  frontend:
    build:
      context: ./Frontend - ReactJS
      dockerfile: Dockerfile
    container_name: clinicpro-frontend
    restart: unless-stopped
    environment:
      VITE_API_BASE_URL: ${VITE_API_BASE_URL:-http://localhost:3000}
    ports:
      - "${FRONTEND_PORT:-80}:80"
    networks:
      - clinicpro-network
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

# Named volumes for persistent data
volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  backend_uploads:
    driver: local

# Custom network for service communication
networks:
  clinicpro-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16