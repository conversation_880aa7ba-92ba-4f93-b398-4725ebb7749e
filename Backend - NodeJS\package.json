{"name": "clinic-pro-backend", "version": "1.0.0", "description": "Backend API for Clinic Management System", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon src/server.ts", "seed": "npx ts-node src/seeds/allSeeders.ts", "seed:clear": "npx ts-node src/seeds/allSeeders.ts --clear", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["clinic", "management", "healthcare", "api", "mongodb"], "author": "", "license": "ISC", "overrides": {"inflight": "npm:@isaacs/caching-transform@^4.0.0", "glob": "^10.3.10", "lodash.get": "npm:lodash@^4.17.21", "lodash.isequal": "npm:lodash@^4.17.21"}, "dependencies": {"@aws-sdk/client-s3": "^3.857.0", "@aws-sdk/s3-request-presigner": "^3.857.0", "@google/genai": "^1.11.0", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "multer": "^2.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.30", "nodemon": "^3.1.10"}}