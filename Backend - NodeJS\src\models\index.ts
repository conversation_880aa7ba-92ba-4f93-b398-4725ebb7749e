export { default as User, IUser } from './User';
export { default as Patient, IPatient } from './Patient';
export { default as Appointment, IAppointment } from './Appointment';
export { default as MedicalRecord, IMedicalRecord } from './MedicalRecord';
export { default as Invoice, IInvoice } from './Invoice';
export { default as Payment, IPayment } from './Payment';
export { default as Payroll, IPayroll } from './Payroll';
export { default as Inventory, IInventory } from './Inventory';
export { default as Lead, ILead } from './Lead';
export { default as Prescription, IPrescription, IMedication } from './Prescription';
export { default as Service, IService } from './Service';
export { default as TestCategory, ITestCategory } from './TestCategory';
export { default as SampleType, ISampleType } from './SampleType';
export { default as TestMethodology, ITestMethodology } from './TestMethodology';
export { default as TurnaroundTime, ITurnaroundTime } from './TurnaroundTime';
export { default as Test, ITest } from './Test';
export { default as TestReport, ITestReport } from './TestReport';
export { default as Department, IDepartment } from './Department';
export { default as LabVendor, ILabVendor } from './LabVendor';
export { default as Expense, IExpense } from './Expense';
export { default as Training, ITraining, ITrainingModule } from './Training';
export { default as TrainingProgress, ITrainingProgress, IModuleProgress } from './TrainingProgress';
export { default as XrayAnalysis, IXrayAnalysis } from './XrayAnalysis'; 