@echo off
REM ClinicPro Docker Deployment Script for Windows
REM This script provides easy deployment commands for the ClinicPro application

setlocal enabledelayedexpansion

REM Function to check if Dock<PERSON> is running
:check_docker
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker and try again.
    exit /b 1
)
goto :eof

REM Function to check if docker-compose is available
:check_docker_compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] docker-compose is not installed. Please install docker-compose and try again.
    exit /b 1
)
goto :eof

REM Function to create .env file if it doesn't exist
:setup_env
if not exist .env (
    echo [WARNING] .env file not found. Creating from .env.example...
    copy .env.example .env >nul
    echo [SUCCESS] .env file created. Please review and update the configuration.
    echo [WARNING] IMPORTANT: Change the default passwords and JWT secret before deploying to production!
)
goto :eof

REM Function to deploy production environment
:deploy_production
echo [INFO] Deploying ClinicPro in production mode...

call :check_docker
if errorlevel 1 exit /b 1

call :check_docker_compose
if errorlevel 1 exit /b 1

call :setup_env

echo [INFO] Building and starting containers...
docker-compose down --remove-orphans
docker-compose up --build -d

echo [INFO] Waiting for services to be healthy...
timeout /t 30 /nobreak >nul

echo [SUCCESS] ClinicPro deployed successfully!
echo [INFO] Frontend: http://localhost:80
echo [INFO] Backend API: http://localhost:3000
echo [INFO] MongoDB: localhost:27017
goto :eof

REM Function to deploy development environment
:deploy_development
echo [INFO] Deploying ClinicPro in development mode...

call :check_docker
if errorlevel 1 exit /b 1

call :check_docker_compose
if errorlevel 1 exit /b 1

call :setup_env

echo [INFO] Building and starting development containers...
docker-compose -f docker-compose.dev.yml down --remove-orphans
docker-compose -f docker-compose.dev.yml up --build -d

echo [INFO] Waiting for services to be ready...
timeout /t 30 /nobreak >nul

echo [SUCCESS] ClinicPro development environment deployed!
echo [INFO] Frontend: http://localhost:5173
echo [INFO] Backend API: http://localhost:3000
echo [INFO] MongoDB: localhost:27017
goto :eof

REM Function to stop all services
:stop_services
echo [INFO] Stopping ClinicPro services...
docker-compose down
docker-compose -f docker-compose.dev.yml down
echo [SUCCESS] All services stopped.
goto :eof

REM Function to show logs
:show_logs
if "%~2"=="" (
    docker-compose logs -f
) else (
    docker-compose logs -f %2
)
goto :eof

REM Function to show status
:show_status
echo [INFO] ClinicPro Service Status:
docker-compose ps
goto :eof

REM Function to clean up
:cleanup
echo [INFO] Cleaning up Docker resources...
docker-compose down --volumes --remove-orphans
docker-compose -f docker-compose.dev.yml down --volumes --remove-orphans
docker system prune -f
echo [SUCCESS] Cleanup completed.
goto :eof

REM Main script logic
if "%1"=="production" goto deploy_production
if "%1"=="prod" goto deploy_production
if "%1"=="development" goto deploy_development
if "%1"=="dev" goto deploy_development
if "%1"=="stop" goto stop_services
if "%1"=="logs" goto show_logs
if "%1"=="status" goto show_status
if "%1"=="cleanup" goto cleanup

echo ClinicPro Docker Deployment Script
echo.
echo Usage: %0 {production^|development^|stop^|logs^|status^|cleanup}
echo.
echo Commands:
echo   production   - Deploy in production mode
echo   development  - Deploy in development mode
echo   stop         - Stop all services
echo   logs [service] - Show logs (optionally for specific service)
echo   status       - Show service status
echo   cleanup      - Clean up all Docker resources
echo.
echo Examples:
echo   %0 production
echo   %0 development
echo   %0 logs backend
echo   %0 stop
exit /b 1